This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.6.22)  7 SEP 2025 23:05
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**OIC_User_Participation_Research
(./OIC_User_Participation_Research.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(d:/latex/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(d:/latex/texlive/2025/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (d:/latex/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18


Package inputenc Warning: inputenc package ignored with utf8 based engines.

) (d:/latex/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+lmr on input line 116.
 (d:/latex/texlive/2025/texmf-dist/tex/latex/lm/t1lmr.fd
File: t1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/ctex.sty (d:/latex/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (d:/latex/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count266
\l__pdf_internal_box=\box52
\g__pdf_backend_annotation_int=\count267
\g__pdf_backend_link_int=\count268
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (d:/latex/texlive/2025/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count269
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen142
\g__ctex_section_depth_int=\count270
\g__ctex_font_size_int=\count271
 (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
 (d:/latex/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (d:/latex/texlive/2025/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count272
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count273
\l__xeCJK_begin_int=\count274
\l__xeCJK_end_int=\count275
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count276
\g__xeCJK_node_int=\count277
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count278
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count279

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count280
\l__xeCJK_fam_int=\count281
\g__xeCJK_fam_allocation_int=\count282
\l__xeCJK_verb_case_int=\count283
\l__xeCJK_verb_exspace_skip=\skip57
 (d:/latex/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.sty (d:/latex/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (d:/latex/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count284
\l__fontspec_language_int=\count285
\l__fontspec_strnum_int=\count286
\l__fontspec_tmp_int=\count287
\l__fontspec_tmpa_int=\count288
\l__fontspec_tmpb_int=\count289
\l__fontspec_tmpc_int=\count290
\l__fontspec_em_int=\count291
\l__fontspec_emdef_int=\count292
\l__fontspec_strong_int=\count293
\l__fontspec_strongdef_int=\count294
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172
 (d:/latex/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (d:/latex/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (d:/latex/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174
 (d:/latex/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count295
\l__zhnum_tmp_int=\count296
 (d:/latex/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CTEX)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)

Package fontspec Info: 
(fontspec)             Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"

)) (d:/latex/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
) (d:/latex/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (d:/latex/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count297
\Gm@cntv=\count298
\c@Gm@tempcnt=\count299
\Gm@bindingoffset=\dimen175
\Gm@wd@mp=\dimen176
\Gm@odd@mp=\dimen177
\Gm@even@mp=\dimen178
\Gm@layoutwidth=\dimen179
\Gm@layoutheight=\dimen180
\Gm@layouthoffset=\dimen181
\Gm@layoutvoffset=\dimen182
\Gm@dimlist=\toks20
) (d:/latex/texlive/2025/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (d:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(d:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen183
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen184
) (d:/latex/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count300
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count301
\leftroot@=\count302
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count303
\DOTSCASE@=\count304
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen185
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count305
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count306
\dotsspace@=\muskip17
\c@parentequation=\count307
\dspbrk@lvl=\count308
\tag@help=\toks22
\row@=\count309
\column@=\count310
\maxfields@=\count311
\andhelp@=\toks23
\eqnshift@=\dimen186
\alignsep@=\dimen187
\tagshift@=\dimen188
\tagwidth@=\dimen189
\totwidth@=\dimen190
\lineht@=\dimen191
\@envbody=\toks24
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (d:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (d:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen192
\Gin@req@width=\dimen193
) (d:/latex/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen194
\lightrulewidth=\dimen195
\cmidrulewidth=\dimen196
\belowrulesep=\dimen197
\belowbottomsep=\dimen198
\aboverulesep=\dimen199
\abovetopsep=\dimen256
\cmidrulesep=\dimen257
\cmidrulekern=\dimen258
\defaultaddspace=\dimen259
\@cmidla=\count312
\@cmidlb=\count313
\@aboverulesep=\dimen260
\@belowrulesep=\dimen261
\@thisruleclass=\count314
\@lastruleclass=\count315
\@thisrulewidth=\dimen262
) (d:/latex/texlive/2025/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2024-10-27 v4.22 Multi-page Table package (DPC)
\LTleft=\skip62
\LTright=\skip63
\LTpre=\skip64
\LTpost=\skip65
\LTchunksize=\count316
\LTcapwidth=\dimen263
\LT@head=\box58
\LT@firsthead=\box59
\LT@foot=\box60
\LT@lastfoot=\box61
\LT@gbox=\box62
\LT@cols=\count317
\LT@rows=\count318
\c@LT@tables=\count319
\c@LT@chunks=\count320
\LT@p@ftn=\toks26
) (d:/latex/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen264
\ar@mcellbox=\box63
\extrarowheight=\dimen265
\NC@list=\toks27
\extratabsurround=\skip66
\backup@length=\skip67
\ar@cellbox=\box64
) (d:/latex/texlive/2025/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
 (d:/latex/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (d:/latex/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count321
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape.sty
Package: pdflscape 2022-10-27 v0.13 Display of landscape pages in PDF
 (d:/latex/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape-nometadata.sty
Package: pdflscape-nometadata 2022-10-28 v0.13 Display of landscape pages in PDF (HO)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/lscape.sty
Package: lscape 2020/05/28 v3.02 Landscape Pages (DPC)
)
Package pdflscape Info: Auto-detected driver: dvipdfm (xetex) on input line 98.
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/tools/afterpage.sty
Package: afterpage 2023/07/04 v1.08 After-Page Package (DPC)
\AP@output=\toks28
\AP@partial=\box65
\AP@footins=\box66
) (d:/latex/texlive/2025/texmf-dist/tex/latex/capt-of/capt-of.sty
Package: capt-of 2009/12/29 v0.2 standard captions outside of floats
) (d:/latex/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (d:/latex/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/latex/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/latex/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/latex/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (d:/latex/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/latex/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/latex/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (d:/latex/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count322
) (d:/latex/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen266
\Hy@linkcounter=\count323
\Hy@pagecounter=\count324
 (d:/latex/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
) (d:/latex/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count325
 (d:/latex/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count326
 (d:/latex/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen267
 (d:/latex/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/latex/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count327
\Field@Width=\dimen268
\Fld@charsize=\dimen269
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (d:/latex/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count328
\c@Item=\count329
\c@Hfootnote=\count330
)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/latex/texlive/2025/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box67
\c@Hy@AnnotLevel=\count331
\HyField@AnnotCount=\count332
\Fld@listcount=\count333
\c@bookmark@seq@number=\count334
 (d:/latex/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (d:/latex/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip68
) (d:/latex/texlive/2025/texmf-dist/tex/latex/cleveref/cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
) (d:/latex/texlive/2025/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip69
\bibsep=\skip70
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count335
) (d:/latex/texlive/2025/texmf-dist/tex/latex/doi/doi.sty
Package: doi 2018/09/09 handle doi numbers
)
Package hyperref Info: Option `colorlinks' set `true' on input line 52.
 (./OIC_User_Participation_Research.aux)
\openout1 = `OIC_User_Participation_Research.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 62.
LaTeX Font Info:    ... okay on input line 62.

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 62.
LaTeX Font Info:    Redeclaring math accent \acute on input line 62.
LaTeX Font Info:    Redeclaring math accent \grave on input line 62.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 62.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 62.
LaTeX Font Info:    Redeclaring math accent \bar on input line 62.
LaTeX Font Info:    Redeclaring math accent \breve on input line 62.
LaTeX Font Info:    Redeclaring math accent \check on input line 62.
LaTeX Font Info:    Redeclaring math accent \hat on input line 62.
LaTeX Font Info:    Redeclaring math accent \dot on input line 62.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 62.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 62.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 62.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 62.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 62.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 62.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 62.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 62.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (d:/latex/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx))
Package hyperref Info: Link coloring ON on input line 62.
 (./OIC_User_Participation_Research.out) (./OIC_User_Participation_Research.out)
\@outlinefile=\write3
\openout3 = `OIC_User_Participation_Research.out'.



Package hyperref Warning: Rerun to get /PageLabels entry.

LaTeX Font Info:    Trying to load font information for U+msa on input line 65.
(d:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 65.
 (d:/latex/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

] (./OIC_User_Participation_Research.toc)
\tf@toc=\write4
\openout4 = `OIC_User_Participation_Research.toc'.



[2]

[3]

[4]

[5]

[6] (./OIC_User_Participation_Research.bbl

[7]

[8]
Underfull \hbox (badness 7963) in paragraph at lines 77--81
[]\TU/lmr/m/n/12 Nasir, M. and Zaki, M. (2016).  Social support and user engagement
 []


Underfull \hbox (badness 10000) in paragraph at lines 77--81
\TU/lmr/m/n/12 in online health communities.  Retrieved September 7, 2025, from
 []


Overfull \hbox (237.13193pt too wide) in paragraph at lines 77--81
\TU/lmr/m/n/12 https://www.researchgate.net/publication/289580778_Social_Support_and_User_Engagement_in_Online_Health_Communities. 
 []



[9])

[10] (./OIC_User_Participation_Research.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2022/07/14>
 ***********
Package rerunfilecheck Info: File `OIC_User_Participation_Research.out' has not changed.
(rerunfilecheck)             Checksum: 516CAEBE4F0734F2B6C375A2C380B011;2143.
 ) 
Here is how much of TeX's memory you used:
 18052 strings out of 473832
 394699 string characters out of 5730404
 830540 words of memory out of 5000000
 40838 multiletter control sequences out of 15000+600000
 571338 words of font info for 101 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,7n,111p,542b,495s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on OIC_User_Participation_Research.pdf (10 pages).
