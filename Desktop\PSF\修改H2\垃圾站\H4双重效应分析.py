#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
H4双重效应分析
============

专门分析H4假设：早期活跃度的双重效应
- 理解什么是"双重效应"
- 如何测量倒U型关系
- 验证适度投入vs过度投入的效应

作者：AI助手
日期：2025-01-21
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

class H4DoubleEffectAnalyzer:
    """H4双重效应分析器"""
    
    def __init__(self):
        """初始化分析器"""
        print("🔬 H4双重效应分析器")
        print("="*50)
        print("🎯 目标：理解早期活跃度的双重效应机制")
        print("📊 理论：适度投入保护效应 vs 过度投入燃尽效应")

    def load_and_analyze_h4_data(self, filename):
        """加载数据并分析H4相关变量"""
        print(f"\n📁 加载数据分析H4变量...")
        
        try:
            df = pd.read_csv(filename)
            print(f"   ✅ 数据加载成功: {df.shape}")
            
            # 识别H4相关变量
            h4_variables = {
                'early_activity': 'early_activity',  # 原始早期活跃度
                'early_activity_log': 'early_activity_log',  # 对数变换
                'active_months': 'active_months',  # 活跃月数
                'community_age_months': 'community_age_months'  # 社区年龄
            }
            
            # 检查变量是否存在
            available_h4_vars = {}
            for var_name, col_name in h4_variables.items():
                if col_name in df.columns:
                    available_h4_vars[var_name] = col_name
                    print(f"   ✅ 找到H4变量: {col_name}")
                else:
                    print(f"   ❌ 未找到: {col_name}")
            
            if not available_h4_vars:
                print("   ❌ 未找到任何H4相关变量")
                return None
            
            # 目标变量
            y = df['event_status'].astype(int)  # 1=流失, 0=留存
            
            return df, available_h4_vars, y
            
        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return None, None, None

    def analyze_early_activity_distribution(self, df, h4_vars):
        """分析早期活跃度的分布"""
        print(f"\n📊 早期活跃度分布分析...")
        
        for var_name, col_name in h4_vars.items():
            if col_name in df.columns:
                data = df[col_name]
                
                print(f"\n🔍 {var_name} ({col_name}) 分布:")
                print(f"   均值: {data.mean():.3f}")
                print(f"   中位数: {data.median():.3f}")
                print(f"   标准差: {data.std():.3f}")
                print(f"   最小值: {data.min():.3f}")
                print(f"   最大值: {data.max():.3f}")
                print(f"   25%分位数: {data.quantile(0.25):.3f}")
                print(f"   75%分位数: {data.quantile(0.75):.3f}")
                
                # 检查是否有零值（可能表示无早期活动）
                zero_count = (data == 0).sum()
                print(f"   零值数量: {zero_count} ({zero_count/len(data):.1%})")

    def test_linear_vs_nonlinear_relationship(self, df, h4_vars, y):
        """测试线性vs非线性关系"""
        print(f"\n🔬 线性vs非线性关系检验...")
        
        results = {}
        
        for var_name, col_name in h4_vars.items():
            if col_name in df.columns:
                x = df[col_name]
                
                print(f"\n📈 分析 {var_name}:")
                
                # 1. 线性相关性
                linear_corr, linear_p = stats.pearsonr(x, y)
                print(f"   线性相关系数: r = {linear_corr:.4f}, p = {linear_p:.6f}")
                
                # 2. 创建二次项
                x_squared = x ** 2
                
                # 3. 二次项相关性
                quad_corr, quad_p = stats.pearsonr(x_squared, y)
                print(f"   二次项相关系数: r = {quad_corr:.4f}, p = {quad_p:.6f}")
                
                # 4. 多项式回归检验
                from sklearn.preprocessing import PolynomialFeatures
                from sklearn.linear_model import LinearRegression
                from sklearn.metrics import r2_score
                
                # 线性模型
                X_linear = x.values.reshape(-1, 1)
                lr_linear = LinearRegression()
                lr_linear.fit(X_linear, y)
                y_pred_linear = lr_linear.predict(X_linear)
                r2_linear = r2_score(y, y_pred_linear)
                
                # 二次模型
                poly_features = PolynomialFeatures(degree=2)
                X_poly = poly_features.fit_transform(X_linear)
                lr_poly = LinearRegression()
                lr_poly.fit(X_poly, y)
                y_pred_poly = lr_poly.predict(X_poly)
                r2_poly = r2_score(y, y_pred_poly)
                
                print(f"   线性模型R²: {r2_linear:.4f}")
                print(f"   二次模型R²: {r2_poly:.4f}")
                print(f"   R²提升: {r2_poly - r2_linear:.4f}")
                
                # 5. 判断是否存在非线性关系
                nonlinear_evidence = r2_poly > r2_linear + 0.01  # 至少提升1%
                print(f"   非线性证据: {'✅ 是' if nonlinear_evidence else '❌ 否'}")
                
                # 6. 分组分析：检验倒U型关系
                # 将数据分为三组：低、中、高
                x_tertiles = pd.qcut(x, q=3, labels=['低', '中', '高'])
                group_means = df.groupby(x_tertiles)['event_status'].mean()
                
                print(f"   分组流失率:")
                for group, rate in group_means.items():
                    print(f"      {group}活跃度: {rate:.3f}")
                
                # 检验是否为倒U型（中间组流失率最低）
                is_inverted_u = (group_means['中'] < group_means['低']) and (group_means['中'] < group_means['高'])
                print(f"   倒U型模式: {'✅ 是' if is_inverted_u else '❌ 否'}")
                
                results[var_name] = {
                    'linear_corr': linear_corr,
                    'linear_p': linear_p,
                    'quad_corr': quad_corr,
                    'quad_p': quad_p,
                    'r2_linear': r2_linear,
                    'r2_poly': r2_poly,
                    'r2_improvement': r2_poly - r2_linear,
                    'nonlinear_evidence': nonlinear_evidence,
                    'group_means': group_means.to_dict(),
                    'inverted_u_pattern': is_inverted_u
                }
        
        return results

    def visualize_double_effect(self, df, h4_vars, y):
        """可视化双重效应"""
        print(f"\n📊 可视化双重效应...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('H4: 早期活跃度双重效应分析', fontsize=16, fontweight='bold')
        
        plot_idx = 0
        for var_name, col_name in h4_vars.items():
            if col_name in df.columns and plot_idx < 4:
                ax = axes[plot_idx // 2, plot_idx % 2]
                
                x = df[col_name]
                
                # 创建分箱
                n_bins = 20
                bins = pd.cut(x, bins=n_bins)
                bin_means = df.groupby(bins)['event_status'].agg(['mean', 'count'])
                
                # 只保留样本量足够的分箱
                valid_bins = bin_means[bin_means['count'] >= 5]
                
                if len(valid_bins) > 0:
                    # 获取分箱中点
                    bin_centers = [interval.mid for interval in valid_bins.index]
                    flow_rates = valid_bins['mean'].values
                    
                    # 绘制散点图
                    ax.scatter(bin_centers, flow_rates, alpha=0.6, s=valid_bins['count']*2)
                    
                    # 拟合趋势线
                    if len(bin_centers) > 3:
                        z = np.polyfit(bin_centers, flow_rates, 2)  # 二次拟合
                        p = np.poly1d(z)
                        x_smooth = np.linspace(min(bin_centers), max(bin_centers), 100)
                        ax.plot(x_smooth, p(x_smooth), 'r--', alpha=0.8, linewidth=2)
                    
                    ax.set_xlabel(f'{var_name}')
                    ax.set_ylabel('流失率')
                    ax.set_title(f'{var_name} vs 流失率')
                    ax.grid(True, alpha=0.3)
                
                plot_idx += 1
        
        # 隐藏多余的子图
        for i in range(plot_idx, 4):
            axes[i // 2, i % 2].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('H4_double_effect_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("   ✅ 图表已保存为: H4_double_effect_analysis.png")

    def interpret_double_effect_mechanism(self, results):
        """解释双重效应机制"""
        print(f"\n🎯 双重效应机制解释...")
        print("="*50)
        
        print("📚 理论背景:")
        print("   H4假设基于两个理论:")
        print("   1. 投资模型理论 - 早期投入创造沉没成本，增加留存")
        print("   2. 工作要求-资源模型 - 过度投入导致资源耗竭，引发燃尽")
        print()
        
        print("🔍 测量方法:")
        print("   1. early_activity: 用户注册后早期的活动水平")
        print("   2. active_months: 用户总的活跃月数")
        print("   3. 通过分组分析检验倒U型关系")
        print("   4. 通过多项式回归检验非线性效应")
        print()
        
        print("📊 结果解释:")
        for var_name, result in results.items():
            print(f"\n🔬 {var_name}:")
            
            if result['inverted_u_pattern']:
                print("   ✅ 发现倒U型模式 - 支持双重效应假设")
                print("   📈 机制解释:")
                print("      - 低活跃度: 投入不足，缺乏承诺感")
                print("      - 中等活跃度: 适度投入，最佳留存效果")
                print("      - 高活跃度: 过度投入，可能导致燃尽")
            else:
                print("   ❌ 未发现明显的倒U型模式")
                
                if result['linear_corr'] > 0:
                    print("   📈 呈现线性正相关 - 更多投入带来更好留存")
                elif result['linear_corr'] < 0:
                    print("   📉 呈现线性负相关 - 更多投入反而降低留存")
                else:
                    print("   ➡️ 无明显关系")
            
            print(f"   📊 统计指标:")
            print(f"      线性相关: r={result['linear_corr']:.3f}")
            print(f"      非线性改善: ΔR²={result['r2_improvement']:.3f}")
            print(f"      分组流失率: {result['group_means']}")

    def run_h4_comprehensive_analysis(self, filename):
        """运行H4综合分析"""
        print("🚀 启动H4双重效应综合分析")
        print("="*60)
        
        # 加载数据
        df, h4_vars, y = self.load_and_analyze_h4_data(filename)
        if df is None:
            return None
        
        # 分析分布
        self.analyze_early_activity_distribution(df, h4_vars)
        
        # 测试关系
        results = self.test_linear_vs_nonlinear_relationship(df, h4_vars, y)
        
        # 可视化
        self.visualize_double_effect(df, h4_vars, y)
        
        # 解释机制
        self.interpret_double_effect_mechanism(results)
        
        return results


if __name__ == "__main__":
    # 创建分析器
    analyzer = H4DoubleEffectAnalyzer()
    
    # 查找数据文件
    data_files = [
        'user_survival_analysis_dataset_90days_cleaned.csv',
        'user_survival_analysis_dataset_150days_cleaned.csv',
        'user_survival_analysis_dataset_180days_cleaned.csv',
        'user_survival_analysis_dataset_330days_cleaned.csv'
    ]
    
    # 运行分析
    for filename in data_files:
        try:
            print(f"🎯 分析文件: {filename}")
            results = analyzer.run_h4_comprehensive_analysis(filename)
            if results:
                break
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            continue
    else:
        print("❌ 未找到可用的数据文件")
