#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
处理评论数据和四阈值数据集，进行用户匹配

作者: AI助手
日期: 2025-01-11
版本: 1.0
"""

import pandas as pd
import numpy as np
import os
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class DataProcessor:
    """数据处理器"""
    
    def __init__(self, project_root: str = "../../.."):
        """
        初始化数据处理器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = project_root
        self.comment_data = None
        self.user_comments = {}
        self.threshold_datasets = {}
        
        # 四阈值数据集文件名
        self.threshold_files = {
            90: "user_survival_analysis_dataset_90days_cleaned.csv",
            150: "user_survival_analysis_dataset_150days_cleaned.csv",
            180: "user_survival_analysis_dataset_180days_cleaned.csv",
            330: "user_survival_analysis_dataset_330days_cleaned.csv"
        }
        
        print("📊 数据处理器初始化完成")
    
    def load_comment_data(self, comment_file: str = "元数据/评论信息.csv") -> bool:
        """
        加载评论数据
        
        Args:
            comment_file: 评论文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            comment_path = os.path.join(self.project_root, comment_file)
            print(f"📂 加载评论数据: {comment_path}")
            
            self.comment_data = pd.read_csv(comment_path)
            
            print(f"✅ 评论数据加载成功: {self.comment_data.shape}")
            print(f"   总评论数: {len(self.comment_data)}")
            print(f"   用户数: {self.comment_data['comment_uid'].nunique()}")
            print(f"   有效评论数: {self.comment_data['comment_content'].notna().sum()}")
            print(f"   有效率: {self.comment_data['comment_content'].notna().mean()*100:.1f}%")
            
            # 聚合用户评论
            self._aggregate_user_comments()
            
            return True
            
        except Exception as e:
            print(f"❌ 评论数据加载失败: {e}")
            return False
    
    def _aggregate_user_comments(self):
        """聚合用户评论"""
        print("🔄 聚合用户评论...")
        
        # 按用户聚合评论
        user_comment_groups = self.comment_data.groupby('comment_uid')['comment_content'].apply(
            lambda x: ' '.join(x.dropna().astype(str))
        )
        
        # 转换为字典，并统计信息
        self.user_comments = {}
        for user_id, comments in user_comment_groups.items():
            if comments.strip():  # 只保留非空评论
                self.user_comments[str(user_id)] = comments.strip()
        
        print(f"   聚合完成: {len(self.user_comments)} 个用户有有效评论")
        
        # 统计评论长度分布
        comment_lengths = [len(text) for text in self.user_comments.values()]
        if comment_lengths:
            print(f"   评论长度统计: 平均{np.mean(comment_lengths):.0f}字, 中位数{np.median(comment_lengths):.0f}字")
            print(f"   最短{min(comment_lengths)}字, 最长{max(comment_lengths)}字")
    
    def load_threshold_datasets(self) -> bool:
        """加载四阈值数据集"""
        print("📂 加载四阈值数据集...")
        
        success_count = 0
        for threshold, filename in self.threshold_files.items():
            filepath = os.path.join(self.project_root, filename)
            
            try:
                # 特殊处理180天数据集的编码问题
                if threshold == 180:
                    try:
                        df = pd.read_csv(filepath, encoding='utf-8')
                    except:
                        try:
                            df = pd.read_csv(filepath, encoding='gbk')
                        except:
                            df = pd.read_csv(filepath, encoding='latin-1')
                else:
                    df = pd.read_csv(filepath)
                
                self.threshold_datasets[threshold] = df
                
                print(f"✅ {threshold}天数据集: {df.shape}")
                
                # 查找用户ID列
                user_id_col = self._find_user_id_column(df)
                if user_id_col:
                    unique_users = df[user_id_col].nunique()
                    print(f"   用户ID列: {user_id_col}, 用户数: {unique_users}")
                else:
                    print(f"   ⚠️ 未找到明确的用户ID列")
                
                success_count += 1
                
            except Exception as e:
                print(f"❌ {threshold}天数据集加载失败: {e}")
        
        print(f"\n📊 成功加载 {success_count}/{len(self.threshold_files)} 个数据集")
        return success_count > 0
    
    def _find_user_id_column(self, df: pd.DataFrame) -> Optional[str]:
        """查找用户ID列"""
        # 常见的用户ID列名
        possible_names = ['uid', 'user_id', 'userId', 'user_uid', 'comment_uid', 'id']
        
        for col in df.columns:
            if col in possible_names:
                return col
            
            # 模糊匹配
            col_lower = col.lower()
            if 'uid' in col_lower and df[col].dtype in ['int64', 'float64', 'object']:
                return col
        
        return None
    
    def match_users_with_comments(self, threshold: int) -> Tuple[List[str], List[str], List[str]]:
        """
        匹配阈值数据集用户与评论数据
        
        Args:
            threshold: 时间阈值
            
        Returns:
            Tuple: (有评论用户列表, 无评论用户列表, 用户ID列名)
        """
        if threshold not in self.threshold_datasets:
            raise ValueError(f"阈值 {threshold} 的数据集未加载")
        
        df = self.threshold_datasets[threshold]
        user_id_col = self._find_user_id_column(df)
        
        if not user_id_col:
            raise ValueError(f"{threshold}天数据集中未找到用户ID列")
        
        print(f"\n🔗 匹配 {threshold}天数据集用户与评论...")
        
        # 获取所有用户ID
        all_users = df[user_id_col].astype(str).tolist()
        
        # 分类用户
        users_with_comments = []
        users_without_comments = []
        
        for user_id in all_users:
            if user_id in self.user_comments:
                users_with_comments.append(user_id)
            else:
                users_without_comments.append(user_id)
        
        # 统计匹配情况
        total_users = len(all_users)
        matched_users = len(users_with_comments)
        match_rate = matched_users / total_users * 100
        
        print(f"   总用户数: {total_users}")
        print(f"   有评论用户: {matched_users} ({match_rate:.2f}%)")
        print(f"   无评论用户: {len(users_without_comments)} ({100-match_rate:.2f}%)")
        
        return users_with_comments, users_without_comments, user_id_col
    
    def get_user_comment_text(self, user_id: str) -> str:
        """
        获取用户的评论文本
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 用户的所有评论文本
        """
        return self.user_comments.get(str(user_id), "")
    
    def get_dataset_info(self) -> Dict:
        """获取数据集信息"""
        info = {
            'comment_data': {
                'loaded': self.comment_data is not None,
                'total_comments': len(self.comment_data) if self.comment_data is not None else 0,
                'unique_users': self.comment_data['comment_uid'].nunique() if self.comment_data is not None else 0,
                'users_with_comments': len(self.user_comments)
            },
            'threshold_datasets': {}
        }
        
        for threshold, df in self.threshold_datasets.items():
            user_id_col = self._find_user_id_column(df)
            info['threshold_datasets'][threshold] = {
                'shape': df.shape,
                'user_id_column': user_id_col,
                'unique_users': df[user_id_col].nunique() if user_id_col else 0
            }
        
        return info
    
    def save_user_comment_mapping(self, output_path: str = "user_comment_mapping.json"):
        """保存用户评论映射"""
        try:
            # 统计信息
            mapping_info = {
                'total_users_with_comments': len(self.user_comments),
                'comment_length_stats': {},
                'user_comments': self.user_comments
            }
            
            # 计算评论长度统计
            if self.user_comments:
                lengths = [len(text) for text in self.user_comments.values()]
                mapping_info['comment_length_stats'] = {
                    'mean': float(np.mean(lengths)),
                    'median': float(np.median(lengths)),
                    'min': int(min(lengths)),
                    'max': int(max(lengths)),
                    'std': float(np.std(lengths))
                }
            
            # 保存到文件
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(mapping_info, f, ensure_ascii=False, indent=2)
            
            print(f"💾 用户评论映射已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 用户评论映射保存失败: {e}")
            return False
    
    def create_sample_dataset(self, threshold: int, sample_size: int = 100) -> pd.DataFrame:
        """
        创建样本数据集用于测试
        
        Args:
            threshold: 时间阈值
            sample_size: 样本大小
            
        Returns:
            pd.DataFrame: 样本数据集
        """
        if threshold not in self.threshold_datasets:
            raise ValueError(f"阈值 {threshold} 的数据集未加载")
        
        df = self.threshold_datasets[threshold]
        
        # 随机采样
        if len(df) > sample_size:
            sample_df = df.sample(n=sample_size, random_state=42)
        else:
            sample_df = df.copy()
        
        print(f"📋 创建 {threshold}天数据集样本: {sample_df.shape}")
        
        return sample_df

def main():
    """主函数 - 测试数据处理"""
    print("📊 数据处理模块测试")
    print("="*50)
    
    # 创建处理器
    processor = DataProcessor()
    
    # 加载数据
    comment_success = processor.load_comment_data()
    threshold_success = processor.load_threshold_datasets()
    
    if comment_success and threshold_success:
        # 测试用户匹配
        for threshold in [90, 150, 180, 330]:
            if threshold in processor.threshold_datasets:
                try:
                    users_with, users_without, user_col = processor.match_users_with_comments(threshold)
                    print(f"✅ {threshold}天数据集匹配完成")
                except Exception as e:
                    print(f"❌ {threshold}天数据集匹配失败: {e}")
        
        # 保存用户评论映射
        processor.save_user_comment_mapping()
        
        # 显示数据集信息
        info = processor.get_dataset_info()
        print(f"\n📊 数据集信息:")
        print(f"   评论数据: {info['comment_data']}")
        print(f"   阈值数据集: {len(info['threshold_datasets'])} 个")
        
        print("\n✅ 数据处理测试完成")
    else:
        print("\n❌ 数据处理测试失败")

if __name__ == "__main__":
    main()
