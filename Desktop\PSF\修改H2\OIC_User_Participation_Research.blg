This is BibTeX, Version 0.99d (TeX Live 2025)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: OIC_User_Participation_Research.aux
The style file: apalike.bst
Database file #1: references.bib
You've used 25 entries,
            1935 wiz_defined-function locations,
            642 strings with 9001 characters,
and the built_in function-call counts, 10193 in all, are:
= -- 994
> -- 438
< -- 10
+ -- 160
- -- 146
* -- 942
:= -- 1790
add.period$ -- 75
call.type$ -- 25
change.case$ -- 198
chr.to.int$ -- 25
cite$ -- 25
duplicate$ -- 345
empty$ -- 699
format.name$ -- 187
if$ -- 1941
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 24
newline$ -- 128
num.names$ -- 75
pop$ -- 138
preamble$ -- 1
purify$ -- 198
quote$ -- 0
skip$ -- 260
stack$ -- 0
substring$ -- 781
swap$ -- 24
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 150
warning$ -- 0
while$ -- 87
width$ -- 0
write$ -- 326
