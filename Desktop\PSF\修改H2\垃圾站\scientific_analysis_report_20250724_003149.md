# 严格统计分析报告
## 基于透明假设检验的特征重要性分析

**分析时间**: 2025-07-24 00:31:49

## 执行摘要
本研究对 70 个特征进行了严格的统计分析。
经过Bonferroni校正，发现 37 个显著特征；
经过FDR校正，发现 50 个显著特征。
模型测试AUC为 0.9437。

## 方法论
### 统计框架
- **假设检验**: Neyman-Pearson框架
- **置换检验**: 10,000次置换计算精确p值
- **效应大小**: <PERSON>'s d标准化效应大小
- **多重校正**: Bonferroni和FDR方法
- **数据分割**: 80%训练，20%测试

### 功效分析
- 单个检验功效: 1.000
- Bonferroni校正功效: 1.000
- 功效是否充足: 是

## 主要结果
### Bonferroni校正显著特征
| 特征名称 | 原始p值 | 校正p值 | 效应大小 | 效应等级 |
|---------|---------|---------|----------|----------|
| active_months | 0.000000 | 0.000000 | 2.568 | Large |
| total_posts_log | 0.000000 | 0.000000 | 1.887 | Large |
| in_degree_centrality | 0.000000 | 0.000000 | 1.735 | Large |
| in_degree_as_post_author_log | 0.000000 | 0.000000 | 1.642 | Large |
| received_comments_count_log | 0.000000 | 0.000000 | 1.554 | Large |
| in_degree_weighted_mention_log | 0.000000 | 0.000000 | 1.514 | Large |
| total_interactions_log | 0.000000 | 0.000000 | 1.416 | Large |
| total_posts | 0.000000 | 0.000000 | 1.265 | Large |
| pagerank | 0.000000 | 0.000000 | 1.100 | Large |
| any_strong_negative_comment | 0.000000 | 0.000000 | 1.086 | Large |
| received_comments_count | 0.000000 | 0.000000 | 1.064 | Large |
| in_degree_as_post_author | 0.000000 | 0.000000 | 1.048 | Large |
| currentLevel | 0.000000 | 0.000000 | 1.047 | Large |
| any_strong_positive_comment | 0.000000 | 0.000000 | 0.923 | Large |
| interactions_L90D_log | 0.000000 | 0.000000 | 0.894 | Large |
| community_age_months | 0.000000 | 0.000000 | 0.889 | Large |
| received_comments_count_L90D_log | 0.000000 | 0.000000 | 0.885 | Large |
| negative_feedback_ratio_L90D | 0.000000 | 0.000000 | 0.875 | Large |
| out_degree_centrality | 0.000000 | 0.000000 | 0.873 | Large |
| sentiment_score_std | 0.000000 | 0.000000 | 0.862 | Large |
| total_comments_made_log | 0.000000 | 0.000000 | 0.797 | Medium |
| has_received_comments | 0.000000 | 0.000000 | 0.790 | Medium |
| in_degree_weighted_mention | 0.000000 | 0.000000 | 0.785 | Medium |
| betweenness_centrality | 0.000000 | 0.000000 | 0.782 | Medium |
| interaction_trend_L90D_vs_avg | 0.000000 | 0.000000 | 0.770 | Medium |
| negative_feedback_ratio_L30D | 0.000000 | 0.000000 | 0.727 | Medium |
| total_interactions | 0.000000 | 0.000000 | 0.683 | Medium |
| posts_L90D | 0.000000 | 0.000000 | 0.645 | Medium |
| avg_browseNum_log | 0.000000 | 0.000000 | 0.636 | Medium |
| positive_feedback_ratio_L90D | 0.000000 | 0.000000 | 0.628 | Medium |
| negative_feedback_ratio | 0.000000 | 0.000000 | 0.610 | Medium |
| positive_feedback_ratio_L30D | 0.000000 | 0.000000 | 0.594 | Medium |
| received_comments_count_L30D_log | 0.000000 | 0.000000 | 0.571 | Medium |
| positive_feedback_ratio | 0.000000 | 0.000000 | 0.519 | Medium |
| avg_monthly_interactions_log | 0.000000 | 0.000000 | 0.517 | Medium |
| neutral_feedback_ratio | 0.000000 | 0.000000 | 0.502 | Medium |
| isHwEmployee | 0.000000 | 0.000000 | 0.459 | Small |

### FDR校正显著特征
除Bonferroni显著特征外，FDR方法还发现了 13 个特征。

## 结论
发现 37 个特征与目标变量存在强统计关联。
这些结果通过了最严格的多重比较校正。

## 局限性
1. 观察性研究，无法建立因果关系
2. 多重比较校正可能过于保守
3. 效应大小基于Cohen约定
4. 需要在独立数据集上验证

## 透明度声明
- 所有假设预先注册
- 完整的统计过程公开
- 代码和数据可获得
- 无利益冲突