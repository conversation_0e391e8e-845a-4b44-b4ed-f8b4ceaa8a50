#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
词典加载模块
加载和处理大连理工情感词汇和LIWC词典

作者: AI助手
日期: 2025-01-11
版本: 1.0
"""

import pandas as pd
import json
import os
from typing import Dict, List, Set
import warnings
warnings.filterwarnings('ignore')

class DictionaryLoader:
    """词典加载器"""
    
    def __init__(self, base_path: str = ".."):
        """
        初始化词典加载器
        
        Args:
            base_path: 词典文件的基础路径
        """
        self.base_path = base_path
        self.dalian_dict = {}
        self.liwc_dict = {}
        
        print("🔧 词典加载器初始化完成")
    
    def load_dalian_emotions(self, file_path: str = "大连理工 情感词汇.xlsx") -> bool:
        """
        加载大连理工情感词汇
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            full_path = os.path.join(self.base_path, file_path)
            print(f"📂 加载大连理工情感词汇: {full_path}")
            
            # 读取Excel文件
            df = pd.read_excel(full_path)
            print(f"   数据形状: {df.shape}")
            print(f"   列名: {list(df.columns)}")
            
            # 处理数据
            self.dalian_dict = self._process_dalian_data(df)
            
            print(f"✅ 大连理工情感词汇加载成功: {len(self.dalian_dict)} 个词汇")
            
            # 统计各类别词汇数量
            self._print_dalian_statistics()
            
            return True
            
        except Exception as e:
            print(f"❌ 大连理工情感词汇加载失败: {e}")
            return False
    
    def _process_dalian_data(self, df: pd.DataFrame) -> Dict:
        """处理大连理工数据"""
        dalian_dict = {}
        
        # 确定列名
        word_col = '词语' if '词语' in df.columns else df.columns[0]
        emotion_col = '情感分类' if '情感分类' in df.columns else df.columns[1]
        intensity_col = '强度' if '强度' in df.columns else df.columns[2] if len(df.columns) > 2 else None
        
        for idx, row in df.iterrows():
            word = str(row[word_col]).strip()
            emotion = str(row[emotion_col]).strip()
            
            if word and word != 'nan' and emotion and emotion != 'nan':
                # 处理强度
                intensity = 5  # 默认强度
                if intensity_col and pd.notna(row[intensity_col]):
                    try:
                        intensity = float(row[intensity_col])
                    except:
                        intensity = 5
                
                # 确定极性
                polarity = self._determine_polarity(emotion)
                
                dalian_dict[word] = {
                    'emotion': emotion,
                    'intensity': intensity,
                    'polarity': polarity
                }
        
        return dalian_dict
    
    def _determine_polarity(self, emotion: str) -> str:
        """确定情感极性"""
        positive_emotions = ['PH', 'PA', 'PG', '好', '乐', '安', '惊', '怒']
        negative_emotions = ['NN', 'NE', 'ND', 'NB', '哀', '惧', '恶', '羞']
        
        if any(pos in emotion for pos in positive_emotions):
            return 'positive'
        elif any(neg in emotion for neg in negative_emotions):
            return 'negative'
        else:
            return 'neutral'
    
    def _print_dalian_statistics(self):
        """打印大连理工词典统计信息"""
        if not self.dalian_dict:
            return
        
        # 按极性统计
        positive_count = sum(1 for word_info in self.dalian_dict.values() if word_info['polarity'] == 'positive')
        negative_count = sum(1 for word_info in self.dalian_dict.values() if word_info['polarity'] == 'negative')
        neutral_count = sum(1 for word_info in self.dalian_dict.values() if word_info['polarity'] == 'neutral')
        
        print(f"   积极情感词: {positive_count} 个")
        print(f"   消极情感词: {negative_count} 个")
        print(f"   中性情感词: {neutral_count} 个")
        
        # 按情感类别统计
        emotion_counts = {}
        for word_info in self.dalian_dict.values():
            emotion = word_info['emotion']
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        print(f"   情感类别数: {len(emotion_counts)} 个")
        # 显示前5个最多的类别
        top_emotions = sorted(emotion_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        for emotion, count in top_emotions:
            print(f"     {emotion}: {count} 个")
    
    def load_liwc_dictionary(self, file_path: str = "Auto_CLIWC-master/datasets/sc_liwc.dic") -> bool:
        """
        加载LIWC词典
        
        Args:
            file_path: LIWC词典文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            full_path = os.path.join(self.base_path, file_path)
            print(f"📂 加载LIWC词典: {full_path}")
            
            categories = {}
            word_categories = {}
            current_section = None
            
            with open(full_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    # 检查是否是类别定义行 (数字开头，后面跟空格和类别名)
                    if line[0].isdigit() and ' ' in line:
                        parts = line.split(' ', 1)
                        if len(parts) >= 2:
                            cat_id = parts[0].strip()
                            cat_name = parts[1].strip()
                            categories[cat_id] = cat_name
                            current_section = 'categories'
                    elif line == '%':
                        current_section = 'words'
                    elif current_section == 'words':
                        # 处理词汇行 (空格分隔)
                        if ' ' in line:
                            parts = line.split()
                            word = parts[0]
                            cat_ids = parts[1:]

                            word_cats = []
                            for cat_id in cat_ids:
                                if cat_id in categories:
                                    word_cats.append(categories[cat_id])

                            if word_cats:
                                word_categories[word] = word_cats
            
            self.liwc_dict = {
                'categories': categories,
                'word_categories': word_categories
            }
            
            print(f"✅ LIWC词典加载成功:")
            print(f"   类别数: {len(categories)}")
            print(f"   词汇数: {len(word_categories)}")
            
            # 显示主要类别
            self._print_liwc_statistics()
            
            return True
            
        except Exception as e:
            print(f"❌ LIWC词典加载失败: {e}")
            return False
    
    def _print_liwc_statistics(self):
        """打印LIWC词典统计信息"""
        if not self.liwc_dict:
            return
        
        categories = self.liwc_dict['categories']
        word_categories = self.liwc_dict['word_categories']
        
        # 统计每个类别的词汇数量
        category_counts = {}
        for word, cats in word_categories.items():
            for cat in cats:
                category_counts[cat] = category_counts.get(cat, 0) + 1
        
        # 显示重要类别
        important_categories = ['social', 'posemo', 'negemo', 'cogmech', 'insight', 'cause', 'friend', 'family']
        print("   重要类别词汇数:")
        for cat in important_categories:
            count = category_counts.get(cat, 0)
            if count > 0:
                print(f"     {cat}: {count} 个")
    
    def get_social_efficacy_words(self) -> Dict[str, Set[str]]:
        """获取社交效能感相关词汇"""
        social_words = {
            'liwc_social': set(),
            'liwc_friend': set(),
            'liwc_family': set(),
            'dalian_positive_social': set()
        }

        # 🔧 修复：LIWC社交相关词汇（过滤负面词汇）
        if self.liwc_dict:
            # 定义需要过滤的负面社交词汇
            negative_social_words = {
                '仇人', '敌人', '辱骂', '斗争', '仇敌', '打岔', '争吵', '怨恨',
                '嘲骂', '争论', '打架', '侮辱', '仇恨', '嘲讽', '暴民', '辩论'
            }

            word_categories = self.liwc_dict['word_categories']
            for word, categories in word_categories.items():
                if 'social' in categories and word not in negative_social_words:
                    social_words['liwc_social'].add(word)
                if 'friend' in categories:
                    social_words['liwc_friend'].add(word)
                if 'family' in categories:
                    social_words['liwc_family'].add(word)

        # 🔧 修复：大连理工积极社交词汇筛选逻辑
        if self.dalian_dict:
            # 社交关键字
            social_keywords = ['交', '友', '合', '群', '团', '社', '人', '伙', '伴', '聚',
                             '和', '协', '助', '帮', '支持', '配合', '沟通', '联系', '互动']

            for word, info in self.dalian_dict.items():
                # 积极情感 + 包含社交关键字
                if (info['polarity'] == 'positive' and
                    any(keyword in word for keyword in social_keywords)):
                    social_words['dalian_positive_social'].add(word)

        return social_words
    
    def get_emotional_stability_words(self) -> Dict[str, Set[str]]:
        """获取情感稳定性相关词汇"""
        emotion_words = {
            'liwc_positive': set(),
            'liwc_negative': set(),
            'dalian_positive': set(),
            'dalian_negative': set()
        }
        
        # LIWC情感词汇
        if self.liwc_dict:
            word_categories = self.liwc_dict['word_categories']
            for word, categories in word_categories.items():
                if 'posemo' in categories:
                    emotion_words['liwc_positive'].add(word)
                if 'negemo' in categories:
                    emotion_words['liwc_negative'].add(word)
        
        # 大连理工情感词汇
        if self.dalian_dict:
            for word, info in self.dalian_dict.items():
                if info['polarity'] == 'positive':
                    emotion_words['dalian_positive'].add(word)
                elif info['polarity'] == 'negative':
                    emotion_words['dalian_negative'].add(word)
        
        return emotion_words
    
    def save_processed_dictionaries(self, output_path: str = "processed_dictionaries.json"):
        """保存处理后的词典"""
        try:
            # 转换为可序列化的格式
            social_words = self.get_social_efficacy_words()
            emotion_words = self.get_emotional_stability_words()
            
            processed_dict = {
                'social_efficacy': {k: list(v) for k, v in social_words.items()},
                'emotional_stability': {k: list(v) for k, v in emotion_words.items()},
                'dalian_raw': self.dalian_dict,
                'liwc_categories': self.liwc_dict.get('categories', {}),
                'statistics': {
                    'dalian_word_count': len(self.dalian_dict),
                    'liwc_word_count': len(self.liwc_dict.get('word_categories', {})),
                    'social_word_count': sum(len(v) for v in social_words.values()),
                    'emotion_word_count': sum(len(v) for v in emotion_words.values())
                }
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(processed_dict, f, ensure_ascii=False, indent=2)
            
            print(f"💾 处理后的词典已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 词典保存失败: {e}")
            return False

def main():
    """主函数 - 测试词典加载"""
    print("🔧 词典加载模块测试")
    print("="*50)
    
    # 创建加载器
    loader = DictionaryLoader()
    
    # 加载词典
    dalian_success = loader.load_dalian_emotions()
    liwc_success = loader.load_liwc_dictionary()
    
    if dalian_success and liwc_success:
        # 保存处理后的词典
        loader.save_processed_dictionaries()
        
        print("\n✅ 词典加载测试完成")
        print(f"📊 社交效能感词汇: {sum(len(v) for v in loader.get_social_efficacy_words().values())} 个")
        print(f"📊 情感稳定性词汇: {sum(len(v) for v in loader.get_emotional_stability_words().values())} 个")
    else:
        print("\n❌ 词典加载测试失败")

if __name__ == "__main__":
    main()
