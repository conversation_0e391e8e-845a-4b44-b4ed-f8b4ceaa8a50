#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面双语学术图表生成器 - 彻底解决中文字体问题
Comprehensive Bilingual Academic Charts Generator - Complete Chinese Font Solution
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches
import os
import warnings
warnings.filterwarnings('ignore')

def force_chinese_font_ultimate():
    """
    终极中文字体解决方案
    """
    print("🔧 终极中文字体解决方案...")
    
    try:
        # 方法1: 直接指定系统字体文件路径
        import matplotlib.font_manager as fm
        
        # Windows系统字体路径
        font_paths = [
            r'C:\Windows\Fonts\msyh.ttc',      # 微软雅黑
            r'C:\Windows\Fonts\simhei.ttf',    # 黑体
            r'C:\Windows\Fonts\simsun.ttc',    # 宋体
            r'C:\Windows\Fonts\simkai.ttf',    # 楷体
            r'C:\Windows\Fonts\simfang.ttf',   # 仿宋
        ]
        
        # 找到可用的字体
        available_font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                available_font = font_path
                break
        
        if available_font:
            # 创建字体属性对象
            from matplotlib.font_manager import FontProperties
            chinese_font_prop = FontProperties(fname=available_font)
            
            # 设置matplotlib的字体
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 清除字体缓存并重建
            fm._rebuild()
            
            print(f"✅ 成功设置中文字体: {available_font}")
            return chinese_font_prop
        else:
            print("❌ 未找到系统中文字体文件")
            return None
            
    except Exception as e:
        print(f"❌ 字体设置失败: {e}")
        return None

def setup_plot_style():
    """设置图表样式"""
    plt.style.use('default')
    plt.rcParams.update({
        'figure.dpi': 100,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.facecolor': 'white',
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.labelsize': 14,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'legend.fontsize': 11,
        'figure.figsize': (12, 8)
    })

def create_comprehensive_data():
    """
    创建全面的四阈值数据
    """
    # 主效应数据
    main_effects_data = {
        'Variable': [
            'active_months', 'degree_centrality', 'received_comments_count_log',
            'total_interactions_log', 'pagerank', 'closeness_centrality',
            'betweenness_centrality', 'has_received_comments', 'Social_Efficacy_score',
            'early_activity_log', 'Emotional_Stability_score'
        ],
        'Variable_CN': [
            '活跃月数', '度中心性', '收到评论数量', '总互动量', 'PageRank值', 
            '接近中心性', '中介中心性', '是否收到评论', '社交效能感', 
            '早期活动量', '情感稳定性'
        ],
        '90天': [2.5201, 1.6121, 1.5317, 1.4614, 1.1308, 1.0963, 0.8958, 0.7792, 0.5528, 0.3576, 0.1933],
        '150天': [2.2701, 1.4221, 1.3287, 1.3040, 0.9882, 1.0071, 0.7366, 0.7096, 0.5270, 0.2795, 0.1750],
        '180天': [2.1473, 1.3170, 1.2742, 1.2553, 0.9015, 0.9937, 0.6356, 0.7156, 0.5435, 0.2379, 0.1643],
        '330天': [1.4256, 0.8927, 0.9612, 0.9019, 0.6530, 0.8379, 0.4819, 0.6482, 0.4523, 0.1622, 0.1572],
        'p_90': [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.003, 0.052, 0.296],
        'p_150': [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.007, 0.145, 0.034],
        'p_180': [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.003, 0.001, 0.005, 0.217, 0.324],
        'p_330': [0.001, 0.001, 0.001, 0.001, 0.003, 0.001, 0.026, 0.003, 0.034, 0.430, 0.041]
    }
    
    # Social_Efficacy_score中介数据
    social_mediation_data = {
        'Variable': [
            'total_interactions_log', 'has_received_comments', 'received_comments_count_log',
            'degree_centrality', 'pagerank', 'betweenness_centrality',
            'closeness_centrality', 'active_months', 'early_activity_log'
        ],
        'Variable_CN': [
            '总互动量', '是否收到评论', '收到评论数量', '度中心性', 'PageRank值', 
            '中介中心性', '接近中心性', '活跃月数', '早期活动量'
        ],
        '90天': [9.8, 18.8, 10.1, 14.7, 24.7, 14.4, 10.9, 9.3, 39.7],
        '150天': [10.4, 19.7, 11.4, 12.1, 21.3, 11.2, 11.5, 7.7, 45.3],
        '180天': [13.2, 20.9, 13.5, 13.7, 23.5, 16.5, 12.8, 9.3, 55.3],
        '330天': [13.1, 16.4, 12.2, 10.0, 16.0, 5.0, 11.1, 7.0, 56.6]
    }
    
    # Emotional_Stability_score中介数据
    emotional_mediation_data = {
        'Variable': [
            'total_interactions_log', 'has_received_comments', 'received_comments_count_log',
            'degree_centrality', 'pagerank', 'betweenness_centrality',
            'closeness_centrality', 'active_months', 'early_activity_log'
        ],
        'Variable_CN': [
            '总互动量', '是否收到评论', '收到评论数量', '度中心性', 'PageRank值', 
            '中介中心性', '接近中心性', '活跃月数', '早期活动量'
        ],
        '90天': [1.3, -4.7, -3.0, 0.1, -2.6, -1.9, -1.8, 0.9, 5.3],
        '150天': [1.2, -4.8, -3.0, 0.1, -2.2, -1.5, -1.8, 0.7, 5.9],
        '180天': [1.1, -4.6, -2.9, 0.1, -2.2, -2.2, -1.7, 0.7, 6.4],
        '330天': [1.1, -5.0, -3.0, 0.1, -1.9, -0.8, -2.0, 0.7, 8.7]
    }
    
    # 调节效应数据
    moderation_data = {
        'Variable': [
            'total_interactions_log', 'has_received_comments', 'received_comments_count_log',
            'degree_centrality', 'pagerank', 'betweenness_centrality',
            'closeness_centrality', 'active_months', 'early_activity_log'
        ],
        'Variable_CN': [
            '总互动量', '是否收到评论', '收到评论数量', '度中心性', 'PageRank值', 
            '中介中心性', '接近中心性', '活跃月数', '早期活动量'
        ],
        'social_mod_90': [1, 1, 1, 1, 1, 1, 1, 0, 1],
        'social_mod_150': [1, 1, 1, 1, 1, 1, 1, 0, 1],
        'social_mod_180': [1, 1, 1, 1, 1, 1, 1, 0, 1],
        'social_mod_330': [1, 1, 1, 1, 1, 1, 1, 0, 1],
        'emotional_mod_90': [1, 0, 1, 0, 1, 1, 1, 0, 0],
        'emotional_mod_150': [0, 0, 0, 0, 1, 1, 1, 0, 0],
        'emotional_mod_180': [0, 0, 0, 0, 1, 1, 1, 0, 0],
        'emotional_mod_330': [0, 0, 0, 0, 1, 1, 1, 0, 0]
    }
    
    # 模型性能数据
    performance_data = {
        'Threshold': ['90天', '150天', '180天', '330天'],
        'Threshold_EN': ['90 days', '150 days', '180 days', '330 days'],
        'AUC': [0.8383, 0.7933, 0.8038, 0.7662],
        'Accuracy': [0.823, 0.789, 0.798, 0.756],
        'Precision': [0.856, 0.812, 0.823, 0.789],
        'Recall': [0.789, 0.756, 0.767, 0.712],
        'F1_Score': [0.821, 0.783, 0.794, 0.748],
        'Churn_Rate': [95.6, 93.9, 93.4, 87.9],
        'Sample_Size': [2159, 2159, 2154, 2159],
        'Positive_Cases': [95, 135, 142, 261],
        'Negative_Cases': [2064, 2024, 2012, 1898]
    }
    
    return (pd.DataFrame(main_effects_data), 
            pd.DataFrame(social_mediation_data), 
            pd.DataFrame(emotional_mediation_data),
            pd.DataFrame(moderation_data),
            pd.DataFrame(performance_data))

def test_chinese_font(chinese_font_prop):
    """
    测试中文字体显示
    """
    print("🧪 测试中文字体显示...")
    
    # 确保文件夹存在
    os.makedirs('图表', exist_ok=True)
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试数据
    categories = ['主效应分析', '中介效应检验', '调节效应验证', '四阈值对比', '显著性检验']
    values = [85, 92, 78, 88, 95]
    
    # 绘制柱状图
    bars = ax.bar(categories, values, color='skyblue', alpha=0.8, edgecolor='navy')
    
    # 设置中文标签
    if chinese_font_prop:
        ax.set_title('中文字体显示测试', fontproperties=chinese_font_prop, fontsize=16, fontweight='bold')
        ax.set_xlabel('分析类型', fontproperties=chinese_font_prop, fontsize=14)
        ax.set_ylabel('显著性水平 (%)', fontproperties=chinese_font_prop, fontsize=14)
        
        # 设置x轴标签
        ax.set_xticks(range(len(categories)))
        ax.set_xticklabels(categories, fontproperties=chinese_font_prop, rotation=45, ha='right')
    else:
        ax.set_title('中文字体显示测试', fontsize=16, fontweight='bold')
        ax.set_xlabel('分析类型', fontsize=14)
        ax.set_ylabel('显著性水平 (%)', fontsize=14)
        ax.set_xticklabels(categories, rotation=45, ha='right')
    
    # 添加数值标签
    for i, (bar, val) in enumerate(zip(bars, values)):
        ax.text(i, val + 1, f'{val}%', ha='center', va='bottom', fontweight='bold')
    
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 100)
    
    # 保存测试图表
    plt.tight_layout()
    plt.savefig('图表/中文字体测试_最终版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ 中文字体测试完成")
    return True

if __name__ == "__main__":
    # 设置中文字体
    chinese_font_prop = force_chinese_font_ultimate()
    setup_plot_style()
    
    # 测试中文字体
    test_chinese_font(chinese_font_prop)
    
    # 创建数据
    df_main, df_social, df_emotional, df_moderation, df_performance = create_comprehensive_data()
    
    print(f"\n🎉 字体设置完成！")
    print(f"📁 测试图表保存在: 图表/ 文件夹")
    print(f"🔍 请检查中文字体测试图表")
    print(f"📊 数据准备完成，包含四阈值完整分析结果")
    
    if chinese_font_prop:
        print(f"✅ 中文字体设置成功，可以生成中英文双版本图表")
    else:
        print(f"⚠️ 中文字体设置失败，将只生成英文版本图表")
