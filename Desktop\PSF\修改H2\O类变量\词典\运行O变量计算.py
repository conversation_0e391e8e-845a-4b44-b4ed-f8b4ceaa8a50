#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
O变量计算启动脚本
一键运行O变量计算，为四阈值数据集添加O变量

作者: AI助手
日期: 2025-01-11
版本: 1.0
"""

import os
import sys
import time
from datetime import datetime

def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")
    
    # 添加代码目录到路径
    code_dir = os.path.join(os.path.dirname(__file__), "代码")
    if code_dir not in sys.path:
        sys.path.insert(0, code_dir)
    
    # 检查必要的库
    required_packages = ['pandas', 'numpy', 'jieba']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {missing_packages}")
        print("请运行: pip install pandas numpy jieba")
        return False
    
    print("✅ 环境检查通过")
    return True

def check_data_files():
    """检查数据文件是否存在"""
    print("📂 检查数据文件...")
    
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    # 检查词典文件
    dalian_file = os.path.join(os.path.dirname(__file__), "大连理工 情感词汇.xlsx")
    liwc_file = os.path.join(os.path.dirname(__file__), "Auto_CLIWC-master", "datasets", "sc_liwc.dic")
    
    # 检查评论数据
    comment_file = os.path.join(base_dir, "元数据", "评论信息.csv")
    
    # 检查四阈值数据集
    threshold_files = [
        os.path.join(base_dir, "user_survival_analysis_dataset_90days_cleaned.csv"),
        os.path.join(base_dir, "user_survival_analysis_dataset_150days_cleaned.csv"),
        os.path.join(base_dir, "user_survival_analysis_dataset_180days_cleaned.csv"),
        os.path.join(base_dir, "user_survival_analysis_dataset_330days_cleaned.csv")
    ]
    
    missing_files = []
    
    # 检查词典文件
    if not os.path.exists(dalian_file):
        missing_files.append("大连理工 情感词汇.xlsx")
    else:
        print(f"✅ 大连理工情感词汇: {dalian_file}")
    
    if not os.path.exists(liwc_file):
        missing_files.append("LIWC词典文件")
    else:
        print(f"✅ LIWC词典: {liwc_file}")
    
    # 检查评论数据
    if not os.path.exists(comment_file):
        missing_files.append("评论信息.csv")
    else:
        print(f"✅ 评论数据: {comment_file}")
    
    # 检查阈值数据集
    existing_thresholds = []
    for i, file_path in enumerate(threshold_files):
        threshold = [90, 150, 180, 330][i]
        if os.path.exists(file_path):
            existing_thresholds.append(threshold)
            print(f"✅ {threshold}天数据集: {file_path}")
        else:
            missing_files.append(f"{threshold}天数据集")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False, []
    
    print(f"✅ 所有数据文件检查通过，可处理阈值: {existing_thresholds}")
    return True, existing_thresholds

def run_o_variable_calculation():
    """运行O变量计算"""
    print("\n🚀 开始O变量计算")
    print("="*60)

    start_time = time.time()
    code_dir = os.path.join(os.path.dirname(__file__), "代码")

    try:
        # 导入主程序
        from main_calculator import OVariableCalculator
        
        # 创建计算器
        calculator = OVariableCalculator()
        
        # 初始化组件
        print("🔧 初始化组件...")
        if not calculator.initialize_components():
            print("❌ 组件初始化失败")
            return False
        
        # 计算所有阈值的O变量
        print("\n🎯 开始计算O变量...")
        success = calculator.calculate_all_thresholds()
        
        end_time = time.time()
        duration = end_time - start_time
        
        if success:
            print(f"\n🎉 O变量计算成功完成!")
            print(f"⏱️ 总耗时: {duration:.1f} 秒")
            print(f"📁 输出文件位置: {os.path.dirname(os.path.dirname(os.path.dirname(__file__)))}")
            
            # 显示输出文件
            print("\n📋 生成的文件:")
            for threshold in calculator.results:
                print(f"   ✅ SOR_enhanced_dataset_{threshold}days.csv")
            print(f"   ✅ O_variable_calculation_report.json")
            
            return True
        else:
            print(f"\n❌ O变量计算失败")
            print(f"⏱️ 耗时: {duration:.1f} 秒")
            return False
            
    except Exception as e:
        print(f"❌ 运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 O变量计算启动脚本")
    print("="*60)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 任务: 为四阈值数据集计算并添加O变量")
    print("📊 变量: Social_Efficacy + Emotional_Stability")
    print("="*60)
    
    # 1. 设置环境
    if not setup_environment():
        print("\n❌ 环境设置失败，程序退出")
        return
    
    # 2. 检查数据文件
    files_ok, available_thresholds = check_data_files()
    if not files_ok:
        print("\n❌ 数据文件检查失败，程序退出")
        print("请确保以下文件存在:")
        print("  - 大连理工 情感词汇.xlsx")
        print("  - Auto_CLIWC-master/datasets/sc_liwc.dic")
        print("  - 元数据/评论信息.csv")
        print("  - user_survival_analysis_dataset_XXdays_cleaned.csv (四个阈值)")
        return
    
    # 3. 确认运行
    print(f"\n📋 准备处理 {len(available_thresholds)} 个时间阈值: {available_thresholds}")
    
    try:
        user_input = input("\n🤔 是否开始计算O变量? (y/n): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("❌ 用户取消操作")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return
    
    # 4. 运行计算
    success = run_o_variable_calculation()
    
    # 5. 总结
    print("\n" + "="*60)
    if success:
        print("🎉 O变量计算任务圆满完成!")
        print("📊 四阈值数据集已成功增强O变量")
        print("🔬 现在可以进行SOR模型分析了")
    else:
        print("❌ O变量计算任务失败")
        print("🔍 请检查错误信息并重试")
    
    print(f"📅 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)

if __name__ == "__main__":
    main()
