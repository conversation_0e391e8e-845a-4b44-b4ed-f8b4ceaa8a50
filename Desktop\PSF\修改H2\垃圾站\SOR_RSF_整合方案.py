#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOR+RSF完整整合方案
将O类变量整合到SOR框架中，并投入Random Survival Forest分析

作者: AI助手
日期: 2025-01-14
版本: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 生存分析和机器学习库
from sksurv.ensemble import RandomSurvivalForest
from sksurv.metrics import concordance_index_censored
from sksurv.preprocessing import OneHotEncoder
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
import joblib

# 统计分析库
from scipy import stats
from scipy.stats import pearsonr
import json
from datetime import datetime
from typing import Dict, List, Tuple

# 设置中文字体显示
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

class SORFrameworkDesigner:
    """SOR框架设计器"""
    
    def __init__(self):
        """初始化SOR框架设计器"""
        self.sor_framework = {}
        self.variable_mapping = {}
        self.theoretical_basis = {}
        
        print("🎯 SOR框架设计器初始化")
        print("📚 理论基础：Stimulus-Organism-Response模型")
        print("🔬 目标：整合O类变量到完整SOR框架")
        print("="*60)
    
    def design_sor_framework(self) -> Dict:
        """
        设计完整的SOR框架
        
        Returns:
            Dict: SOR框架设计方案
        """
        print("🏗️ 设计SOR框架...")
        
        # S (Stimulus) - 刺激变量：外部环境和社区特征
        stimulus_variables = {
            'community_environment': {
                'name': '社区环境刺激',
                'description': '社区的整体环境特征和氛围',
                'variables': [
                    'community_age_months',  # 社区成熟度
                    'avg_browseNum',  # 社区活跃度
                    'isHwEmployee',  # 员工身份标识
                    'isBigVUser',  # 大V用户标识
                    'isAdmin',  # 管理员标识
                    'currentLevel'  # 用户等级
                ],
                'theoretical_basis': '环境心理学理论：外部环境特征影响个体行为'
            },
            'social_stimuli': {
                'name': '社交刺激',
                'description': '来自其他用户的社交互动刺激',
                'variables': [
                    'total_times_mentioned_by_others',  # 被提及次数
                    'count_mentions_to_unmapped_nicknames',  # 提及他人次数
                    'in_degree_resolved_mention',  # 入度提及
                    'in_degree_as_post_author',  # 作为作者的入度
                    'in_degree_weighted_mention'  # 加权入度提及
                ],
                'theoretical_basis': '社会学习理论：他人行为作为刺激影响个体反应'
            },
            'feedback_stimuli': {
                'name': '反馈刺激',
                'description': '来自社区的反馈和评价刺激',
                'variables': [
                    'received_comments_count',  # 收到评论数
                    'avg_received_sentiment',  # 平均收到情感
                    'positive_feedback_ratio',  # 正面反馈比例
                    'negative_feedback_ratio',  # 负面反馈比例
                    'strong_positive_feedback_ratio',  # 强正面反馈比例
                    'strong_negative_feedback_ratio',  # 强负面反馈比例
                    'neutral_feedback_ratio',  # 中性反馈比例
                    'sentiment_score_variance',  # 情感分数方差
                    'sentiment_score_std'  # 情感分数标准差
                ],
                'theoretical_basis': '强化学习理论：反馈作为刺激强化或抑制行为'
            }
        }
        
        # O (Organism) - 机体变量：个体内在特征和状态
        organism_variables = {
            'psychological_traits': {
                'name': '心理特质',
                'description': '个体的心理特征和人格特质',
                'variables': [
                    'Social_Efficacy_score',  # 🆕 社交效能感评分
                    'Emotional_Stability_score',  # 🆕 情感稳定性评分
                    'Social_Efficacy_confidence',  # 🆕 社交效能感置信度
                    'Emotional_Stability_confidence',  # 🆕 情感稳定性置信度
                    'has_comments_dummy'  # 🆕 评论存在性控制变量
                ],
                'theoretical_basis': '个体差异理论：个体心理特质影响对刺激的反应'
            },
            'network_position': {
                'name': '网络位置',
                'description': '个体在社交网络中的位置和中心性',
                'variables': [
                    'pagerank',  # PageRank中心性
                    'in_degree_centrality',  # 入度中心性
                    'out_degree_centrality',  # 出度中心性
                    'degree_centrality',  # 度中心性
                    'closeness_centrality',  # 接近中心性
                    'betweenness_centrality',  # 中介中心性
                    'eigenvector_centrality'  # 特征向量中心性
                ],
                'theoretical_basis': '社会网络理论：网络位置决定个体的影响力和资源获取能力'
            },
            'cognitive_state': {
                'name': '认知状态',
                'description': '个体的认知处理能力和状态',
                'variables': [
                    'avg_social_efficacy',  # 平均社交效能
                    'avg_community_belonging',  # 平均社区归属感
                    'avg_emotional_satisfaction',  # 平均情感满意度
                    'avg_cognitive_engagement',  # 平均认知参与度
                    'o_variable_confidence',  # O变量置信度
                    'o_variable_trend',  # O变量趋势
                    'engagement_pattern'  # 参与模式
                ],
                'theoretical_basis': '认知负荷理论：个体认知状态影响信息处理和决策'
            }
        }
        
        # R (Response) - 反应变量：个体的行为反应和结果
        response_variables = {
            'participation_behavior': {
                'name': '参与行为',
                'description': '个体在社区中的主动参与行为',
                'variables': [
                    'total_posts',  # 总发帖数
                    'total_comments_made',  # 总评论数
                    'total_interactions',  # 总互动数
                    'posts_L30D',  # 近30天发帖数
                    'comments_made_L30D',  # 近30天评论数
                    'interactions_L30D',  # 近30天互动数
                    'posts_L90D',  # 近90天发帖数
                    'comments_made_L90D',  # 近90天评论数
                    'interactions_L90D'  # 近90天互动数
                ],
                'theoretical_basis': '行为主义理论：行为是对刺激的直接反应'
            },
            'engagement_patterns': {
                'name': '参与模式',
                'description': '个体参与的时间模式和趋势',
                'variables': [
                    'active_months',  # 活跃月数
                    'avg_monthly_interactions',  # 平均月互动数
                    'early_activity',  # 早期活跃度
                    'tenure_days',  # 在社区天数
                    'interaction_trend_L30D_vs_early',  # 30天vs早期趋势
                    'interaction_trend_L30D_vs_avg',  # 30天vs平均趋势
                    'interaction_trend_L90D_vs_early',  # 90天vs早期趋势
                    'interaction_trend_L90D_vs_avg',  # 90天vs平均趋势
                    'sentiment_trend_L30D_vs_avg',  # 30天情感趋势
                    'sentiment_trend_L90D_vs_avg'  # 90天情感趋势
                ],
                'theoretical_basis': '时间序列行为理论：行为模式反映个体的适应性反应'
            },
            'survival_outcome': {
                'name': '生存结果',
                'description': '个体在社区中的留存和流失结果',
                'variables': [
                    'event_status',  # 事件状态（流失=1，留存=0）
                    'has_received_comments',  # 是否收到评论
                    'any_strong_negative_comment',  # 是否有强负面评论
                    'any_strong_positive_comment',  # 是否有强正面评论
                    'mention_success_rate'  # 提及成功率
                ],
                'theoretical_basis': '生存分析理论：最终结果是个体对环境适应的综合体现'
            }
        }
        
        # 构建完整SOR框架
        self.sor_framework = {
            'S_Stimulus': stimulus_variables,
            'O_Organism': organism_variables,
            'R_Response': response_variables
        }
        
        # 保存理论基础
        self.theoretical_basis = {
            'framework_theory': 'SOR理论（Stimulus-Organism-Response）',
            'core_principle': '外部刺激通过个体内在机制处理后产生行为反应',
            'application_domain': '开放创新社区用户持续参与行为',
            'innovation_points': [
                '首次将心理测量学O类变量整合到SOR框架',
                '基于文本挖掘的社交效能感和情感稳定性测量',
                '多维度网络中心性作为机体变量的创新应用',
                '四时间阈值的动态SOR效应分析'
            ]
        }
        
        print("✅ SOR框架设计完成")
        self._print_framework_summary()
        
        return self.sor_framework
    
    def _print_framework_summary(self):
        """打印框架摘要"""
        print("\n📊 SOR框架摘要:")
        print("-" * 50)
        
        total_vars = 0
        for component, categories in self.sor_framework.items():
            component_vars = 0
            print(f"\n🔹 {component}:")
            for category, info in categories.items():
                var_count = len(info['variables'])
                component_vars += var_count
                print(f"   • {info['name']}: {var_count}个变量")
            print(f"   小计: {component_vars}个变量")
            total_vars += component_vars
        
        print(f"\n🎯 总计: {total_vars}个变量")
        
        # 显示新增O类变量
        print(f"\n🆕 新增O类变量:")
        new_o_vars = ['Social_Efficacy_score', 'Emotional_Stability_score', 
                      'Social_Efficacy_confidence', 'Emotional_Stability_confidence', 
                      'has_comments_dummy']
        for var in new_o_vars:
            print(f"   ✨ {var}")

class SORRSFIntegrator:
    """SOR+RSF整合器"""
    
    def __init__(self, project_root: str = "."):
        """初始化整合器"""
        self.project_root = project_root
        self.sor_designer = SORFrameworkDesigner()
        self.datasets = {}
        self.sor_framework = {}
        self.rsf_models = {}
        self.results = {}
        
        print("\n🚀 SOR+RSF整合器初始化")
        print("🎯 目标：将O类变量整合到SOR框架并投入RSF分析")
        print("="*60)
    
    def load_enhanced_datasets(self) -> bool:
        """加载增强的数据集"""
        print("📂 加载增强数据集...")
        
        thresholds = [90, 150, 180, 330]
        success_count = 0
        
        for threshold in thresholds:
            try:
                filename = f"SOR_enhanced_dataset_{threshold}days.csv"
                filepath = f"{self.project_root}/{filename}"
                
                df = pd.read_csv(filepath, encoding='utf-8')
                
                # 验证O类变量是否存在
                required_o_vars = ['Social_Efficacy_score', 'Emotional_Stability_score', 
                                 'has_comments_dummy']
                missing_vars = [var for var in required_o_vars if var not in df.columns]
                
                if missing_vars:
                    print(f"❌ {threshold}天数据集缺少O类变量: {missing_vars}")
                    continue
                
                self.datasets[threshold] = df
                success_count += 1
                
                print(f"✅ {threshold}天数据集加载成功: {df.shape}")
                print(f"   包含O类变量: {[var for var in required_o_vars if var in df.columns]}")
                
            except Exception as e:
                print(f"❌ {threshold}天数据集加载失败: {e}")
        
        print(f"\n📊 数据集加载完成: {success_count}/{len(thresholds)} 成功")
        return success_count > 0

    def validate_o_variables(self) -> bool:
        """验证O类变量的数据质量和完整性"""
        print("🔍 验证O类变量数据质量...")

        validation_results = {}

        for threshold, df in self.datasets.items():
            print(f"\n📊 验证{threshold}天阈值数据:")

            # 检查O类变量
            o_vars = ['Social_Efficacy_score', 'Emotional_Stability_score',
                     'Social_Efficacy_confidence', 'Emotional_Stability_confidence',
                     'has_comments_dummy']

            threshold_results = {}

            for var in o_vars:
                if var in df.columns:
                    values = df[var].dropna()

                    result = {
                        'exists': True,
                        'total_count': len(df),
                        'valid_count': len(values),
                        'missing_count': len(df) - len(values),
                        'missing_rate': (len(df) - len(values)) / len(df),
                        'mean': values.mean() if len(values) > 0 else None,
                        'std': values.std() if len(values) > 0 else None,
                        'min': values.min() if len(values) > 0 else None,
                        'max': values.max() if len(values) > 0 else None
                    }

                    print(f"   ✅ {var}:")
                    print(f"      有效值: {result['valid_count']}/{result['total_count']} ({(1-result['missing_rate']):.1%})")
                    if result['mean'] is not None:
                        print(f"      统计: 均值={result['mean']:.2f}, 标准差={result['std']:.2f}")
                        print(f"      范围: [{result['min']:.2f}, {result['max']:.2f}]")

                else:
                    result = {'exists': False}
                    print(f"   ❌ {var}: 变量不存在")

                threshold_results[var] = result

            # 检查has_comments_dummy的分布
            if 'has_comments_dummy' in df.columns:
                dummy_dist = df['has_comments_dummy'].value_counts()
                print(f"   📈 has_comments_dummy分布:")
                print(f"      有评论(1): {dummy_dist.get(1, 0)} ({dummy_dist.get(1, 0)/len(df):.1%})")
                print(f"      无评论(0): {dummy_dist.get(0, 0)} ({dummy_dist.get(0, 0)/len(df):.1%})")

            validation_results[threshold] = threshold_results

        # 保存验证结果
        self.results['o_variable_validation'] = validation_results

        print("\n✅ O类变量验证完成")
        return True

    def build_complete_sor_model(self) -> bool:
        """构建完整的SOR模型"""
        print("\n🏗️ 构建完整SOR模型...")

        sor_models = {}

        for threshold, df in self.datasets.items():
            print(f"\n📊 构建{threshold}天阈值SOR模型:")

            # 根据SOR框架分类变量
            s_variables = []
            o_variables = []
            r_variables = []

            # 提取各类变量
            for component, categories in self.sor_framework.items():
                for category, info in categories.items():
                    available_vars = [var for var in info['variables'] if var in df.columns]

                    if component == 'S_Stimulus':
                        s_variables.extend(available_vars)
                    elif component == 'O_Organism':
                        o_variables.extend(available_vars)
                    elif component == 'R_Response':
                        r_variables.extend(available_vars)

            # 构建特征矩阵
            feature_vars = s_variables + o_variables + [var for var in r_variables if var != 'event_status']
            available_features = [var for var in feature_vars if var in df.columns]

            X = df[available_features].copy()
            y = df['event_status'].copy() if 'event_status' in df.columns else None

            # 处理缺失值 - 只对数值列计算中位数
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns

            # 数值列用中位数填充
            if len(numeric_cols) > 0:
                X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())

            # 非数值列用众数填充
            if len(non_numeric_cols) > 0:
                for col in non_numeric_cols:
                    X[col] = X[col].fillna(X[col].mode().iloc[0] if len(X[col].mode()) > 0 else 'unknown')

            model_info = {
                'threshold': threshold,
                'S_variables': s_variables,
                'O_variables': o_variables,
                'R_variables': r_variables,
                'feature_matrix': X,
                'target': y,
                'total_features': len(available_features),
                'sample_size': len(df)
            }

            sor_models[threshold] = model_info

            print(f"   ✅ SOR模型构建完成:")
            print(f"      S(刺激)变量: {len([v for v in s_variables if v in df.columns])}个")
            print(f"      O(机体)变量: {len([v for v in o_variables if v in df.columns])}个")
            print(f"      R(反应)变量: {len([v for v in r_variables if v in df.columns])}个")
            print(f"      特征总数: {len(available_features)}")
            print(f"      样本数量: {len(df)}")

        self.results['sor_models'] = sor_models
        print("\n✅ 完整SOR模型构建完成")
        return True

def main():
    """主函数"""
    print("🎯 SOR+RSF完整整合方案")
    print("="*60)
    print("📝 任务: 将O类变量整合到SOR框架并投入RSF分析")
    print("🔬 创新: 心理测量学变量 + 网络分析 + 生存分析")
    print("="*60)

    # 创建整合器
    integrator = SORRSFIntegrator(".")

    # 1. 设计SOR框架
    print("\n🏗️ 第一步：设计SOR框架")
    sor_framework = integrator.sor_designer.design_sor_framework()
    integrator.sor_framework = sor_framework

    # 2. 加载增强数据集
    print("\n📂 第二步：加载增强数据集")
    if not integrator.load_enhanced_datasets():
        print("❌ 数据集加载失败，程序退出")
        return

    # 3. 验证O类变量
    print("\n🔍 第三步：O类变量整合验证")
    integrator.validate_o_variables()

    # 4. 构建完整SOR模型
    print("\n🏗️ 第四步：构建完整SOR模型")
    integrator.build_complete_sor_model()

    print("\n🎉 SOR模型构建完成！")
    print("📋 下一步：设计并执行RSF分析...")

if __name__ == "__main__":
    main()
