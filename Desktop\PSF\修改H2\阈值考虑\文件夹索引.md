# 阈值考虑文件夹索引

## 📁 文件夹结构

### 01_分析脚本
**用途**: 存放所有Python分析脚本
**文件**:
- `时间阈值选择的数据驱动分析.py` - 主要数据分析脚本
- `阈值选择可视化分析.py` - 可视化图表生成脚本
- `四阈值关键指标分析.py` - 关键指标详细分析脚本
- `变量选择的数据驱动分析.py` - 变量选择分析脚本
- `运行所有分析.py` - 一键运行所有分析的脚本

### 02_生成图表
**用途**: 存放所有生成的图表文件
**文件**:
- `时间阈值选择的数据驱动分析.png` - 主要分析图表
- `阈值选择的科学依据分析.png` - 科学依据分析图表
- `四阈值关键指标详细分析.png` - 关键指标详细分析图表（**PPT推荐**）
- `变量分布特征分析.png` - 变量分布特征图表
- `变量预测能力分析.png` - 变量预测能力图表

### 03_分析报告
**用途**: 存放分析报告和文档
**文件**:
- `README.md` - 项目总体说明文档
- `从数据角度解释变量选择.md` - 变量选择的数据驱动解释
- `分析结果总结.md` - 完整的分析结果总结

### 04_原始数据
**用途**: 存放相关的原始数据文件（如有需要）

## 🎯 使用指南

### 快速开始
1. 运行 `01_分析脚本/运行所有分析.py` 执行完整分析
2. 查看 `02_生成图表/` 中的图表结果
3. 阅读 `03_分析报告/` 中的详细报告

### PPT制作建议
- **首选图表**: `02_生成图表/四阈值关键指标详细分析.png`
- **备选图表**: `02_生成图表/时间阈值选择的数据驱动分析.png`

### 学术写作参考
- 参考 `03_分析报告/分析结果总结.md` 获取完整的分析结论
- 使用 `03_分析报告/从数据角度解释变量选择.md` 了解变量选择依据

## 📊 核心发现摘要

### 四个时间阈值的数据表现
- **90天**: 留存率100.0%, 用户适应期基准
- **150天**: 留存率86.1%, 首次大规模流失(13.9%)
- **180天**: 留存率81.9%, 承诺决策分水岭
- **330天**: 留存率75.7%, 长期稳定确认

### 选择依据
1. **阶梯式递减**: 留存率呈现清晰的递减趋势
2. **用户分化**: 分化程度从0.000递增到0.088
3. **行为转换**: 每个阈值对应独特的行为模式转换
4. **预测价值**: 组合使用实现最佳预测效果

---
*最后更新: 2025年1月*
