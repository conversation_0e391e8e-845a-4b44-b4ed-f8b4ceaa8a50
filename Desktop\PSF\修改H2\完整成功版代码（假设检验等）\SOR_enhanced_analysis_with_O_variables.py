#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOR增强分析：包含O变量的用户留存预测分析
基于精简核心变量集的科学分析

作者：AI助手
日期：2025-08-15
版本：1.0

核心变量：
- H1 社交互动频率: total_interactions_log, has_received_comments, received_comments_count_log
- H2 网络中心性: degree_centrality, pagerank, betweenness_centrality, closeness_centrality
- H3 反馈存在性: has_received_comments, received_comments_count_log
- H4 早期活跃度: active_months, early_activity_log
- H5 心理机制(O变量): Social_Efficacy_score, Emotional_Stability_score
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 统计分析库
from scipy import stats
from scipy.stats import pearsonr
from statsmodels.stats.multitest import multipletests

# 机器学习库
from sklearn.model_selection import train_test_split, KFold
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.metrics import roc_auc_score, r2_score, classification_report, confusion_matrix

# 中介分析库
import statsmodels.api as sm
from statsmodels.stats.mediation import Mediation

# 设置中文字体显示
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('default')
sns.set_palette("husl")

print("🚀 SOR增强分析系统启动")
print("📊 包含O变量的用户留存预测分析")
print("=" * 80)

class SOREnhancedAnalyzer:
    """SOR增强分析器：包含O变量的完整分析系统"""
    
    def __init__(self, alpha=0.05, n_permutations=10000):
        self.alpha = alpha
        self.n_permutations = n_permutations

        # 🔧 新增：四个天数阈值
        self.threshold_days = [90, 150, 180, 330]
        self.threshold_datasets = {
            90: 'SOR_enhanced_dataset_90days.csv',
            150: 'SOR_enhanced_dataset_150days.csv',
            180: 'SOR_enhanced_dataset_180days.csv',
            330: 'SOR_enhanced_dataset_330days.csv'
        }
        
        # 定义核心变量集（精简版）
        self.core_variables = {
            'H1_social_interaction': {
                'name': '社交互动频率假设',
                'variables': ['total_interactions_log', 'has_received_comments', 'received_comments_count_log']
            },
            'H2_network_centrality': {
                'name': '网络中心性假设',
                'variables': ['degree_centrality', 'pagerank', 'betweenness_centrality', 'closeness_centrality']
            },
            'H3_feedback_existence': {
                'name': '反馈存在性假设',
                'variables': ['has_received_comments', 'received_comments_count_log']
            },
            'H4_early_activity': {
                'name': '早期活跃度假设',
                'variables': ['active_months', 'early_activity_log']
            },
            'H5_psychological_mechanism': {
                'name': '心理机制假设(O变量)',
                'variables': ['Social_Efficacy_score', 'Emotional_Stability_score']
            }
        }
        
        # 所有核心变量的并集
        all_vars = set()
        for hyp_info in self.core_variables.values():
            all_vars.update(hyp_info['variables'])
        self.all_core_variables = list(all_vars)
        
        print(f"✅ 分析器初始化完成")
        print(f"📋 核心变量总数: {len(self.all_core_variables)}")
        print(f"🔬 假设数量: {len(self.core_variables)}")
    
    def load_data(self, file_path='SOR_enhanced_dataset_90days.csv'):
        """加载SOR增强数据集"""
        print(f"\n📂 加载数据集: {file_path}")
        print("-" * 60)
        
        try:
            # 加载数据
            data = pd.read_csv(file_path)
            print(f"✅ 数据加载成功: {data.shape}")
            
            # 检查核心变量是否存在
            missing_vars = [var for var in self.all_core_variables if var not in data.columns]
            if missing_vars:
                print(f"⚠️  缺失变量: {missing_vars}")
                # 移除缺失的变量
                self.all_core_variables = [var for var in self.all_core_variables if var in data.columns]
                print(f"📋 调整后核心变量数: {len(self.all_core_variables)}")
            
            # 分离特征和目标变量
            target_col = 'event_status'
            if target_col not in data.columns:
                raise ValueError(f"目标变量 {target_col} 不存在")
            
            # 提取核心特征
            X = data[self.all_core_variables].copy()
            y = data[target_col].copy()
            
            # 数据质量检查
            print(f"📊 数据质量报告:")
            print(f"   样本数量: {len(data)}")
            print(f"   特征数量: {len(self.all_core_variables)}")
            print(f"   流失率: {np.mean(y):.3f}")
            print(f"   缺失值: {X.isnull().sum().sum()}")
            
            # 处理缺失值
            if X.isnull().sum().sum() > 0:
                print("🔧 处理缺失值...")
                X = X.fillna(X.median())
            
            self.data = data
            self.X = X
            self.y = y
            self.feature_cols = self.all_core_variables
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def permutation_test(self, feature_values, y, feature_name):
        """改进的置换检验计算p值"""
        # 计算观察到的相关系数
        observed_corr = abs(np.corrcoef(feature_values, y)[0, 1])

        # 置换检验
        null_distribution = []
        np.random.seed(42)  # 确保可重复性
        for _ in range(self.n_permutations):
            y_permuted = np.random.permutation(y)
            null_corr = abs(np.corrcoef(feature_values, y_permuted)[0, 1])
            null_distribution.append(null_corr)

        # 计算p值
        extreme_count = np.sum(np.array(null_distribution) >= observed_corr)

        # 改进的p值计算：避免p=0的问题
        if extreme_count == 0:
            # 当没有极端值时，p值上界为 1/n_permutations
            p_value = 1.0 / self.n_permutations
            p_value_note = f"< {1.0/self.n_permutations:.1e}"
        else:
            p_value = extreme_count / self.n_permutations
            p_value_note = f"{p_value:.6f}"

        return p_value, observed_corr, p_value_note

    def parametric_test(self, feature_values, y):
        """传统参数检验作为对比"""
        from scipy.stats import pearsonr

        # Pearson相关检验
        corr, p_val = pearsonr(feature_values, y)

        return abs(corr), p_val

    def calculate_cohens_d(self, feature_values, y):
        """计算Cohen's d效应量"""
        group1 = feature_values[y == 0]  # 未流失
        group2 = feature_values[y == 1]  # 流失
        
        if len(group1) == 0 or len(group2) == 0:
            return 0.0
        
        mean1, mean2 = np.mean(group1), np.mean(group2)
        std1, std2 = np.std(group1, ddof=1), np.std(group2, ddof=1)
        n1, n2 = len(group1), len(group2)
        
        # 合并标准差
        pooled_std = np.sqrt(((n1 - 1) * std1**2 + (n2 - 1) * std2**2) / (n1 + n2 - 2))
        
        if pooled_std == 0:
            return 0.0
        
        cohens_d = (mean2 - mean1) / pooled_std
        return abs(cohens_d)
    
    def analyze_hypothesis(self, hypothesis_id):
        """分析单个假设的显著性"""
        hyp_info = self.core_variables[hypothesis_id]
        hyp_name = hyp_info['name']
        variables = hyp_info['variables']
        
        # 过滤存在的变量
        available_vars = [var for var in variables if var in self.X.columns]
        
        print(f"\n🔍 {hyp_name} 分析")
        print(f"📋 变量数量: {len(available_vars)}/{len(variables)}")
        print("-" * 50)
        
        results = {}
        p_values = []
        
        for var in available_vars:
            feature_values = self.X[var]

            # 置换检验
            p_value_perm, observed_corr, p_value_note = self.permutation_test(feature_values, self.y, var)

            # 参数检验对比
            corr_param, p_value_param = self.parametric_test(feature_values, self.y)

            # 效应量
            cohens_d = self.calculate_cohens_d(feature_values, self.y)

            # 效应量解释
            if cohens_d < 0.2:
                effect_size = "微效应"
            elif cohens_d < 0.5:
                effect_size = "小效应"
            elif cohens_d < 0.8:
                effect_size = "中效应"
            else:
                effect_size = "大效应"

            results[var] = {
                'p_value_permutation': p_value_perm,
                'p_value_parametric': p_value_param,
                'p_value_note': p_value_note,
                'correlation': observed_corr,
                'cohens_d': cohens_d,
                'effect_size': effect_size,
                'significant': p_value_perm < self.alpha
            }

            p_values.append(p_value_perm)

            # 输出结果
            status = "✅ 显著" if p_value_perm < self.alpha else "❌ 不显著"
            print(f"   {var}:")
            print(f"      状态: {status}")
            print(f"      置换检验p值: {p_value_note}")
            print(f"      参数检验p值: {p_value_param:.6f}")
            print(f"      相关系数: {observed_corr:.4f}")
            print(f"      Cohen's d: {cohens_d:.4f} ({effect_size})")
            print()
        
        # 多重比较校正
        if p_values:
            bonf_rejected, bonf_p_corrected, _, _ = multipletests(
                p_values, alpha=self.alpha, method='bonferroni'
            )
            
            significant_count = np.sum(bonf_rejected)
            print(f"📊 {hyp_name} 总结:")
            print(f"   原始显著: {np.sum([r['significant'] for r in results.values()])}/{len(available_vars)}")
            print(f"   Bonferroni校正后显著: {significant_count}/{len(available_vars)}")
            print(f"   假设支持度: {significant_count/len(available_vars):.1%}")
            
            # 更新校正后的显著性
            for i, var in enumerate(available_vars):
                results[var]['bonf_significant'] = bonf_rejected[i]
                results[var]['bonf_p_corrected'] = bonf_p_corrected[i]
        
        return results
    
    def comprehensive_analysis(self):
        """执行完整的假设分析"""
        print(f"\n🎯 开始完整假设分析")
        print("=" * 80)
        
        all_results = {}
        
        # 分析每个假设
        for hyp_id in self.core_variables.keys():
            results = self.analyze_hypothesis(hyp_id)
            all_results[hyp_id] = results
        
        # 生成总结报告
        self.generate_summary_report(all_results)
        
        return all_results
    
    def generate_summary_report(self, all_results):
        """生成总结报告"""
        print(f"\n📋 SOR增强分析总结报告")
        print("=" * 80)
        
        for hyp_id, hyp_info in self.core_variables.items():
            hyp_name = hyp_info['name']
            results = all_results.get(hyp_id, {})
            
            if not results:
                continue
            
            # 计算支持度
            total_vars = len(results)
            significant_vars = sum(1 for r in results.values() if r.get('bonf_significant', False))
            support_rate = significant_vars / total_vars if total_vars > 0 else 0
            
            # 评价等级
            if support_rate >= 0.8:
                evaluation = "🏆 强支持"
            elif support_rate >= 0.6:
                evaluation = "✅ 中等支持"
            elif support_rate >= 0.4:
                evaluation = "⚠️  弱支持"
            else:
                evaluation = "❌ 不支持"
            
            print(f"\n{hyp_name}:")
            print(f"   变量数量: {total_vars}")
            print(f"   显著变量: {significant_vars}")
            print(f"   支持度: {support_rate:.1%}")
            print(f"   评价: {evaluation}")
            
            # 显示显著变量
            significant_vars_list = [var for var, r in results.items() if r.get('bonf_significant', False)]
            if significant_vars_list:
                print(f"   显著变量: {', '.join(significant_vars_list)}")
    
    def evaluate_model_performance(self):
        """评估模型预测性能"""
        print(f"\n🤖 模型性能评估")
        print("-" * 60)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.3, random_state=42, stratify=self.y
        )
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 随机森林模型
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X_train_scaled, y_train)
        
        # 预测和评估
        y_pred = rf.predict(X_test_scaled)
        y_pred_proba = rf.predict_proba(X_test_scaled)[:, 1]
        test_auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"✅ 模型训练完成")
        print(f"📊 测试集AUC: {test_auc:.4f}")
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': self.feature_cols,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n🎯 特征重要性排序:")
        for idx, row in feature_importance.head(10).iterrows():
            print(f"   {row['feature']}: {row['importance']:.4f}")
        
        return {
            'model': rf,
            'scaler': scaler,
            'test_auc': test_auc,
            'feature_importance': feature_importance,
            'y_test': y_test,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba
        }

    def create_visualization(self, results, model_results):
        """创建可视化图表"""
        print(f"\n📊 生成可视化图表")
        print("-" * 60)

        # 设置图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('SOR增强分析可视化报告', fontsize=16, fontweight='bold')

        # 1. 假设支持度对比
        ax1 = axes[0, 0]
        hyp_names = []
        support_rates = []

        for hyp_id, hyp_info in self.core_variables.items():
            hyp_name = hyp_info['name']
            hyp_results = results.get(hyp_id, {})

            if hyp_results:
                total_vars = len(hyp_results)
                significant_vars = sum(1 for r in hyp_results.values() if r.get('bonf_significant', False))
                support_rate = significant_vars / total_vars if total_vars > 0 else 0

                hyp_names.append(hyp_name.replace('假设', ''))
                support_rates.append(support_rate)

        bars = ax1.bar(range(len(hyp_names)), support_rates, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'])
        ax1.set_xlabel('假设类型')
        ax1.set_ylabel('支持度')
        ax1.set_title('各假设支持度对比')
        ax1.set_xticks(range(len(hyp_names)))
        ax1.set_xticklabels(hyp_names, rotation=45, ha='right')
        ax1.set_ylim(0, 1)

        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.1%}', ha='center', va='bottom')

        # 2. 特征重要性
        ax2 = axes[0, 1]
        top_features = model_results['feature_importance'].head(8)
        bars2 = ax2.barh(range(len(top_features)), top_features['importance'], color='skyblue')
        ax2.set_xlabel('重要性分数')
        ax2.set_title('特征重要性排序')
        ax2.set_yticks(range(len(top_features)))
        ax2.set_yticklabels(top_features['feature'], fontsize=10)

        # 3. O变量效应分析
        ax3 = axes[1, 0]
        o_variables = ['Social_Efficacy_score', 'Emotional_Stability_score']
        o_effects = []
        o_pvalues = []

        h5_results = results.get('H5_psychological_mechanism', {})
        for var in o_variables:
            if var in h5_results:
                o_effects.append(h5_results[var]['cohens_d'])
                o_pvalues.append(h5_results[var]['p_value'])
            else:
                o_effects.append(0)
                o_pvalues.append(1)

        colors = ['green' if p < 0.05 else 'red' for p in o_pvalues]
        bars3 = ax3.bar(range(len(o_variables)), o_effects, color=colors, alpha=0.7)
        ax3.set_xlabel('O变量')
        ax3.set_ylabel("Cohen's d (效应量)")
        ax3.set_title('O变量效应量分析')
        ax3.set_xticks(range(len(o_variables)))
        ax3.set_xticklabels(['社交效能感', '情感稳定性'], rotation=45, ha='right')

        # 添加效应量标准线
        ax3.axhline(y=0.2, color='gray', linestyle='--', alpha=0.5, label='小效应')
        ax3.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='中效应')
        ax3.axhline(y=0.8, color='gray', linestyle='--', alpha=0.9, label='大效应')
        ax3.legend()

        # 4. 模型性能指标
        ax4 = axes[1, 1]
        auc_score = model_results['test_auc']

        # 创建AUC可视化
        ax4.pie([auc_score, 1-auc_score], labels=['AUC', ''], colors=['lightgreen', 'lightgray'],
                startangle=90, counterclock=False)
        ax4.set_title(f'模型AUC性能\n{auc_score:.4f}')

        plt.tight_layout()
        plt.savefig('SOR_enhanced_analysis_report.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 可视化图表已生成并保存为 'SOR_enhanced_analysis_report.png'")

    def export_detailed_results(self, results, model_results):
        """导出详细结果到Excel"""
        print(f"\n📄 导出详细结果")
        print("-" * 60)

        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'SOR_enhanced_analysis_results_{timestamp}.xlsx'

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:

            # 1. 假设分析结果
            hypothesis_summary = []
            for hyp_id, hyp_info in self.core_variables.items():
                hyp_name = hyp_info['name']
                hyp_results = results.get(hyp_id, {})

                for var, result in hyp_results.items():
                    hypothesis_summary.append({
                        '假设': hyp_name,
                        '变量': var,
                        '置换检验p值': result['p_value_permutation'],
                        '参数检验p值': result['p_value_parametric'],
                        'p值说明': result['p_value_note'],
                        '校正p值': result.get('bonf_p_corrected', result['p_value_permutation']),
                        '相关系数': result['correlation'],
                        'Cohen_d': result['cohens_d'],
                        '效应大小': result['effect_size'],
                        '原始显著': result['significant'],
                        '校正后显著': result.get('bonf_significant', result['significant'])
                    })

            hypothesis_df = pd.DataFrame(hypothesis_summary)
            hypothesis_df.to_excel(writer, sheet_name='假设分析结果', index=False)

            # 2. 特征重要性
            model_results['feature_importance'].to_excel(writer, sheet_name='特征重要性', index=False)

            # 3. 数据概览
            data_summary = pd.DataFrame({
                '指标': ['样本总数', '特征数量', '流失率', '模型AUC'],
                '数值': [len(self.y), len(self.feature_cols), np.mean(self.y), model_results['test_auc']]
            })
            data_summary.to_excel(writer, sheet_name='数据概览', index=False)

            # 4. O变量专项分析
            o_analysis = []
            h5_results = results.get('H5_psychological_mechanism', {})
            for var in ['Social_Efficacy_score', 'Emotional_Stability_score']:
                if var in h5_results:
                    result = h5_results[var]
                    o_analysis.append({
                        'O变量': var,
                        '中文名': '社交效能感' if 'Social_Efficacy' in var else '情感稳定性',
                        '置换检验p值': result['p_value_permutation'],
                        '参数检验p值': result['p_value_parametric'],
                        'p值说明': result['p_value_note'],
                        '校正p值': result.get('bonf_p_corrected', result['p_value_permutation']),
                        '相关系数': result['correlation'],
                        'Cohen_d': result['cohens_d'],
                        '效应大小': result['effect_size'],
                        '显著性': '显著' if result.get('bonf_significant', result['significant']) else '不显著'
                    })

            o_df = pd.DataFrame(o_analysis)
            o_df.to_excel(writer, sheet_name='O变量分析', index=False)

        print(f"✅ 详细结果已导出到 '{filename}'")

    def mediation_analysis(self):
        """三层中介和调节分析"""
        print(f"\n🎯 开始三层机制分析")
        print("=" * 80)

        # 第一层：核心机制验证
        print(f"\n📊 第一层：核心机制验证 (Core Mechanism Validation)")
        print("=" * 60)

        core_paths = [
            {
                'name': 'M1: 社会地位 → 社交效能 → 留存',
                'S': 'degree_centrality',
                'O': 'Social_Efficacy_score',
                'R': 'event_status',
                'theory': '社会地位是否通过增强自信来留住用户？'
            },
            {
                'name': 'M2: 社交验证 → 社交效能 → 留存',
                'S': 'received_comments_count_log',
                'O': 'Social_Efficacy_score',
                'R': 'event_status',
                'theory': '被社区看见和回应，是否通过增强自信来留住用户？'
            }
        ]

        core_results = []
        for path in core_paths:
            result = self.single_mediation_analysis(path)
            core_results.append(result)

        # 第二层：系统性中介机制探索 (所有S变量)
        print(f"\n📊 第二层：系统性中介机制探索 (Systematic Mediation Analysis)")
        print("=" * 60)
        print("🔬 测试所有S变量通过O变量的中介效应")

        # 获取所有S变量
        all_s_variables = [
            'total_interactions_log', 'has_received_comments', 'received_comments_count_log',
            'degree_centrality', 'pagerank', 'betweenness_centrality', 'closeness_centrality',
            'active_months', 'early_activity_log'
        ]

        key_paths = []
        for i, s_var in enumerate(all_s_variables, 3):  # 从M3开始编号
            key_paths.append({
                'name': f'M{i}: {s_var} → Social_Efficacy_score → 留存',
                'S': s_var,
                'O': 'Social_Efficacy_score',
                'R': 'event_status',
                'theory': f'{s_var}是否通过提升社交效能感来促进用户留存？'
            })
            key_paths.append({
                'name': f'M{i+len(all_s_variables)}: {s_var} → Emotional_Stability_score → 留存',
                'S': s_var,
                'O': 'Emotional_Stability_score',
                'R': 'event_status',
                'theory': f'{s_var}是否通过提升情感稳定性来促进用户留存？'
            })

        key_results = []
        for path in key_paths:
            result = self.single_mediation_analysis(path)
            key_results.append(result)

        # 第三层：系统性调节机制探索 (所有S变量)
        print(f"\n📊 第三层：系统性调节机制探索 (Systematic Moderation Analysis)")
        print("=" * 60)
        print("🔬 测试所有S变量与O变量的调节效应")

        # 子层3.1：行为交互调节 (特殊的S×S调节)
        print(f"\n📋 子层3.1：行为交互调节")
        print("-" * 40)
        moderation_result_behavioral = self.moderation_analysis_behavioral_inertia()
        moderation_result_efficacy = self.moderation_analysis_efficacy_leverage()

        # 子层3.2：情感稳定性系统性调节 (所有S变量 × Emotional_Stability_score)
        print(f"\n📋 子层3.2：情感稳定性系统性调节")
        print("-" * 40)
        moderation_results_emotional = self.systematic_emotional_stability_moderation()

        # 子层3.3：社交效能感系统性调节 (所有S变量 × Social_Efficacy_score)
        print(f"\n📋 子层3.3：社交效能感系统性调节")
        print("-" * 40)
        moderation_results_social = self.systematic_social_efficacy_moderation()

        return {
            'core_results': core_results,
            'key_results': key_results,
            'moderation_result_behavioral': moderation_result_behavioral,
            'moderation_result_efficacy': moderation_result_efficacy,
            'moderation_results_emotional': moderation_results_emotional,
            'moderation_results_social': moderation_results_social
        }

    def single_mediation_analysis(self, path_info):
        """科学严谨的中介分析"""
        print(f"\n🔍 {path_info['name']}")
        print(f"💡 理论问题: {path_info['theory']}")
        print("-" * 50)

        S = path_info['S']  # 自变量
        O = path_info['O']  # 中介变量
        R = path_info['R']  # 因变量

        # 检查变量是否存在
        if S not in self.X.columns or O not in self.X.columns:
            print(f"❌ 变量缺失: {S} 或 {O}")
            return None

        # 获取数据
        X_s = self.X[S].values
        X_o = self.X[O].values
        y_original = self.y.values

        # 🔧 修正1: 创建留存变量 (retention = 1 - event_status)
        y_retention = 1 - y_original  # 现在1=留存, 0=流失

        # 🔧 修正2: 统一标准化（保持变量间相对关系）
        scaler = StandardScaler()
        data_to_scale = np.column_stack([X_s, X_o])
        scaled_data = scaler.fit_transform(data_to_scale)
        X_s_scaled = scaled_data[:, 0]
        X_o_scaled = scaled_data[:, 1]

        # 🔧 修正3: 使用逻辑回归处理二分类变量
        from sklearn.linear_model import LogisticRegression

        # 步骤1: S → R (总效应 c)
        model_c = LogisticRegression()
        model_c.fit(X_s_scaled.reshape(-1, 1), y_retention)
        c_coef = model_c.coef_[0][0]

        # 步骤2: S → O (路径 a) - 中介变量是连续的，用线性回归
        model_a = LinearRegression()
        model_a.fit(X_s_scaled.reshape(-1, 1), X_o_scaled)
        a_coef = model_a.coef_[0]

        # 步骤3: S + O → R (直接效应 c' 和 路径 b)
        X_combined = np.column_stack([X_s_scaled, X_o_scaled])
        model_cb = LogisticRegression()
        model_cb.fit(X_combined, y_retention)
        c_prime_coef = model_cb.coef_[0][0]  # 直接效应
        b_coef = model_cb.coef_[0][1]       # 中介变量效应

        # 计算中介效应
        indirect_effect = a_coef * b_coef

        # 计算效应比例
        if abs(c_coef) > 1e-10:
            mediation_ratio = abs(indirect_effect / c_coef)
        else:
            mediation_ratio = 0

        # 🔧 修正4: 改进的Bootstrap置信区间
        bootstrap_effects = []
        n_bootstrap = 5000  # 增加Bootstrap次数提高精度
        n_samples = len(X_s_scaled)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            # 重采样
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_s_boot = X_s_scaled[indices]
            X_o_boot = X_o_scaled[indices]
            y_boot = y_retention[indices]

            # 重新计算路径系数
            try:
                # 路径 a: S → O (线性回归)
                model_a_boot = LinearRegression()
                model_a_boot.fit(X_s_boot.reshape(-1, 1), X_o_boot)
                a_boot = model_a_boot.coef_[0]

                # 路径 b: S + O → R (逻辑回归)
                X_combined_boot = np.column_stack([X_s_boot, X_o_boot])
                model_cb_boot = LogisticRegression(max_iter=1000)
                model_cb_boot.fit(X_combined_boot, y_boot)
                b_boot = model_cb_boot.coef_[0][1]

                indirect_boot = a_boot * b_boot
                bootstrap_effects.append(indirect_boot)
            except:
                continue

        # 计算置信区间
        if bootstrap_effects:
            ci_lower = np.percentile(bootstrap_effects, 2.5)
            ci_upper = np.percentile(bootstrap_effects, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 🔧 修正8: 添加Sobel检验
        sobel_se = np.sqrt(a_coef**2 * np.var(bootstrap_effects) + b_coef**2 * np.var([a_coef] * len(bootstrap_effects)))
        sobel_z = indirect_effect / sobel_se if sobel_se > 0 else 0
        sobel_p = 2 * (1 - stats.norm.cdf(abs(sobel_z)))

        # 输出结果
        print(f"📈 路径分析结果:")
        print(f"   总效应 (c): {c_coef:.4f}")
        print(f"   路径 a (S→O): {a_coef:.4f}")
        print(f"   路径 b (O→R): {b_coef:.4f}")
        print(f"   直接效应 (c'): {c_prime_coef:.4f}")
        print(f"   间接效应 (a×b): {indirect_effect:.4f}")
        print(f"   中介比例: {mediation_ratio:.1%}")
        print(f"   Bootstrap 95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   Sobel检验: Z = {sobel_z:.4f}, p = {sobel_p:.6f}")

        # 综合判断显著性
        bootstrap_significant = not (ci_lower <= 0 <= ci_upper)
        sobel_significant = sobel_p < 0.05
        overall_significant = bootstrap_significant and sobel_significant

        if overall_significant:
            status = "✅ 显著中介 (Bootstrap + Sobel)"
        elif bootstrap_significant:
            status = "⚠️ 部分支持 (仅Bootstrap显著)"
        elif sobel_significant:
            status = "⚠️ 部分支持 (仅Sobel显著)"
        else:
            status = "❌ 无显著中介"

        print(f"   中介效应: {status}")

        # 理论解释
        if overall_significant:
            if indirect_effect > 0:
                print(f"   💡 解释: {S}通过提升{O}来促进用户留存")
            else:
                print(f"   💡 解释: {S}通过降低{O}来促进用户留存")

        return {
            'path_name': path_info['name'],
            'theory': path_info['theory'],
            'total_effect': c_coef,
            'path_a': a_coef,
            'path_b': b_coef,
            'direct_effect': c_prime_coef,
            'indirect_effect': indirect_effect,
            'mediation_ratio': mediation_ratio,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant
        }

    def moderation_analysis(self):
        """调节分析：情感稳定性的心理缓冲效应"""
        print(f"\n🔍 C1: 情感稳定性的心理缓冲效应")
        print(f"💡 理论问题: Emotional_Stability_score是否调节has_received_comments对留存的影响？")
        print("-" * 50)

        # 变量设置
        X_var = 'has_received_comments'  # 自变量
        M_var = 'Emotional_Stability_score'  # 调节变量
        Y_var = 'event_status'  # 因变量

        # 检查变量是否存在
        if X_var not in self.X.columns or M_var not in self.X.columns:
            print(f"❌ 变量缺失: {X_var} 或 {M_var}")
            return None

        # 获取数据
        X = self.X[X_var].values
        M = self.X[M_var].values
        Y_original = self.y.values

        # 🔧 修正5: 创建留存变量用于调节分析
        Y_retention = 1 - Y_original  # 现在1=留存, 0=流失

        # 中心化处理
        X_centered = X - np.mean(X)
        M_centered = M - np.mean(M)

        # 创建交互项
        XM_interaction = X_centered * M_centered

        # 构建回归模型
        predictors = np.column_stack([X_centered, M_centered, XM_interaction])

        # 🔧 修正6: 使用逻辑回归分析留存
        model = LogisticRegression(max_iter=1000)
        model.fit(predictors, Y_retention)

        # 获取系数
        coef_X = model.coef_[0][0]      # 主效应X
        coef_M = model.coef_[0][1]      # 主效应M
        coef_XM = model.coef_[0][2]     # 交互效应

        # 简单斜率分析
        # 高情感稳定性 (+1SD)
        M_high = np.std(M)
        simple_slope_high = coef_X + coef_XM * M_high

        # 低情感稳定性 (-1SD)
        M_low = -np.std(M)
        simple_slope_low = coef_X + coef_XM * M_low

        # 🔧 修正7: 改进的交互效应显著性检验
        bootstrap_interactions = []
        n_bootstrap = 5000  # 增加Bootstrap次数
        n_samples = len(X)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot = X_centered[indices]
            M_boot = M_centered[indices]
            Y_boot = Y_retention[indices]
            XM_boot = X_boot * M_boot

            try:
                predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                model_boot = LogisticRegression(max_iter=1000)
                model_boot.fit(predictors_boot, Y_boot)
                bootstrap_interactions.append(model_boot.coef_[0][2])
            except:
                continue

        # 计算置信区间
        if bootstrap_interactions:
            ci_lower = np.percentile(bootstrap_interactions, 2.5)
            ci_upper = np.percentile(bootstrap_interactions, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 输出结果
        print(f"📈 调节分析结果:")
        print(f"   主效应 (has_received_comments): {coef_X:.4f}")
        print(f"   主效应 (Emotional_Stability): {coef_M:.4f}")
        print(f"   交互效应: {coef_XM:.4f}")
        print(f"   95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   高情感稳定性时的简单斜率: {simple_slope_high:.4f}")
        print(f"   低情感稳定性时的简单斜率: {simple_slope_low:.4f}")

        status = "✅ 显著调节" if significant else "❌ 无显著调节"
        print(f"   调节效应: {status}")

        if significant:
            if abs(simple_slope_high) < abs(simple_slope_low):
                interpretation = "情感稳定性起到心理缓冲作用"
            else:
                interpretation = "情感稳定性起到增强作用"
            print(f"   💡 解释: {interpretation}")

        return {
            'main_effect_X': coef_X,
            'main_effect_M': coef_M,
            'interaction_effect': coef_XM,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant,
            'simple_slope_high': simple_slope_high,
            'simple_slope_low': simple_slope_low
        }

    def moderation_analysis_behavioral_inertia(self):
        """路径B: 行为惯性缓冲假说"""
        print(f"\n🔍 路径B: 行为惯性缓冲假说")
        print(f"💡 理论问题: active_months是否调节total_interactions_log对留存的影响？")
        print(f"🎯 核心假设: 对于老用户，互动总量的影响显著弱于新用户")
        print("-" * 50)

        # 变量设置
        X_var = 'total_interactions_log'  # 自变量：总互动数
        M_var = 'active_months'  # 调节变量：活跃月数
        Y_var = 'event_status'  # 因变量：留存状态

        # 检查变量是否存在
        if X_var not in self.X.columns or M_var not in self.X.columns:
            print(f"❌ 变量缺失: {X_var} 或 {M_var}")
            return None

        # 获取数据
        X = self.X[X_var].values
        M = self.X[M_var].values
        Y_original = self.y.values

        # 创建留存变量
        Y_retention = 1 - Y_original  # 现在1=留存, 0=流失

        # 中心化处理
        X_centered = X - np.mean(X)
        M_centered = M - np.mean(M)

        # 创建交互项
        XM_interaction = X_centered * M_centered

        # 构建回归模型
        predictors = np.column_stack([X_centered, M_centered, XM_interaction])

        # 逻辑回归
        model = LogisticRegression(max_iter=1000)
        model.fit(predictors, Y_retention)

        # 获取系数
        coef_X = model.coef_[0][0]      # 主效应X
        coef_M = model.coef_[0][1]      # 主效应M
        coef_XM = model.coef_[0][2]     # 交互效应

        # 简单斜率分析
        # 高活跃月数 (+1SD) - 老用户
        M_high = np.std(M)
        simple_slope_high = coef_X + coef_XM * M_high

        # 低活跃月数 (-1SD) - 新用户
        M_low = -np.std(M)
        simple_slope_low = coef_X + coef_XM * M_low

        # Bootstrap检验交互项系数
        bootstrap_interactions = []
        n_bootstrap = 5000
        n_samples = len(X)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot = X_centered[indices]
            M_boot = M_centered[indices]
            Y_boot = Y_retention[indices]
            XM_boot = X_boot * M_boot

            try:
                predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                model_boot = LogisticRegression(max_iter=1000)
                model_boot.fit(predictors_boot, Y_boot)
                bootstrap_interactions.append(model_boot.coef_[0][2])
            except:
                continue

        # 计算置信区间
        if bootstrap_interactions:
            ci_lower = np.percentile(bootstrap_interactions, 2.5)
            ci_upper = np.percentile(bootstrap_interactions, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 输出结果
        print(f"📈 行为惯性缓冲分析结果:")
        print(f"   主效应 (total_interactions_log): {coef_X:.4f}")
        print(f"   主效应 (active_months): {coef_M:.4f}")
        print(f"   交互效应: {coef_XM:.4f}")
        print(f"   95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   新用户时的简单斜率: {simple_slope_low:.4f}")
        print(f"   老用户时的简单斜率: {simple_slope_high:.4f}")

        status = "✅ 显著调节" if significant else "❌ 无显著调节"
        print(f"   调节效应: {status}")

        if significant:
            if abs(simple_slope_low) > abs(simple_slope_high):
                interpretation = "✅ 假设得到支持：老用户的互动效应弱于新用户"
            else:
                interpretation = "❌ 假设未得到支持：老用户的互动效应强于新用户"
            print(f"   💡 解释: {interpretation}")

        return {
            'main_effect_X': coef_X,
            'main_effect_M': coef_M,
            'interaction_effect': coef_XM,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant,
            'simple_slope_high': simple_slope_high,
            'simple_slope_low': simple_slope_low,
            'hypothesis_supported': significant and abs(simple_slope_low) > abs(simple_slope_high)
        }

    def moderation_analysis_efficacy_leverage(self):
        """路径C: 效能杠杆假说"""
        print(f"\n🔍 路径C: 效能杠杆假说")
        print(f"💡 理论问题: Social_Efficacy_score是否放大degree_centrality对留存的影响？")
        print(f"🎯 核心假设: 高社交效能感用户的网络地位效应更强")
        print("-" * 50)

        # 变量设置
        X_var = 'degree_centrality'  # 自变量：度中心性
        M_var = 'Social_Efficacy_score'  # 调节变量：社交效能感
        Y_var = 'event_status'  # 因变量：留存状态

        # 检查变量是否存在
        if X_var not in self.X.columns or M_var not in self.X.columns:
            print(f"❌ 变量缺失: {X_var} 或 {M_var}")
            return None

        # 获取数据
        X = self.X[X_var].values
        M = self.X[M_var].values
        Y_original = self.y.values

        # 创建留存变量
        Y_retention = 1 - Y_original  # 现在1=留存, 0=流失

        # 中心化处理
        X_centered = X - np.mean(X)
        M_centered = M - np.mean(M)

        # 创建交互项
        XM_interaction = X_centered * M_centered

        # 构建回归模型
        predictors = np.column_stack([X_centered, M_centered, XM_interaction])

        # 逻辑回归
        model = LogisticRegression(max_iter=1000)
        model.fit(predictors, Y_retention)

        # 获取系数
        coef_X = model.coef_[0][0]      # 主效应X
        coef_M = model.coef_[0][1]      # 主效应M
        coef_XM = model.coef_[0][2]     # 交互效应

        # 简单斜率分析
        # 高社交效能感 (+1SD)
        M_high = np.std(M)
        simple_slope_high = coef_X + coef_XM * M_high

        # 低社交效能感 (-1SD)
        M_low = -np.std(M)
        simple_slope_low = coef_X + coef_XM * M_low

        # Bootstrap检验交互项系数
        bootstrap_interactions = []
        n_bootstrap = 5000
        n_samples = len(X)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot = X_centered[indices]
            M_boot = M_centered[indices]
            Y_boot = Y_retention[indices]
            XM_boot = X_boot * M_boot

            try:
                predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                model_boot = LogisticRegression(max_iter=1000)
                model_boot.fit(predictors_boot, Y_boot)
                bootstrap_interactions.append(model_boot.coef_[0][2])
            except:
                continue

        # 计算置信区间
        if bootstrap_interactions:
            ci_lower = np.percentile(bootstrap_interactions, 2.5)
            ci_upper = np.percentile(bootstrap_interactions, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 输出结果
        print(f"📈 效能杠杆分析结果:")
        print(f"   主效应 (degree_centrality): {coef_X:.4f}")
        print(f"   主效应 (Social_Efficacy_score): {coef_M:.4f}")
        print(f"   交互效应: {coef_XM:.4f}")
        print(f"   95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   低效能感时的简单斜率: {simple_slope_low:.4f}")
        print(f"   高效能感时的简单斜率: {simple_slope_high:.4f}")

        status = "✅ 显著调节" if significant else "❌ 无显著调节"
        print(f"   调节效应: {status}")

        if significant:
            if abs(simple_slope_high) > abs(simple_slope_low):
                interpretation = "✅ 假设得到支持：高效能感放大了网络地位效应"
            else:
                interpretation = "❌ 假设未得到支持：高效能感未能放大网络地位效应"
            print(f"   💡 解释: {interpretation}")

        return {
            'main_effect_X': coef_X,
            'main_effect_M': coef_M,
            'interaction_effect': coef_XM,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant,
            'simple_slope_high': simple_slope_high,
            'simple_slope_low': simple_slope_low,
            'hypothesis_supported': significant and abs(simple_slope_high) > abs(simple_slope_low)
        }

    def moderation_analysis_degree_centrality(self):
        """路径D1: 网络地位的情感稳定性调节 (基于Social Identity Theory)"""
        print(f"\n🔍 路径D1: 网络地位的情感稳定性调节")
        print(f"💡 理论基础: Social Identity Theory (Tajfel & Turner, 1979)")
        print(f"🎯 核心假设: 情感稳定性缓冲网络地位变化对留存的影响")
        print("-" * 50)

        # 变量设置
        X_var = 'degree_centrality'  # 自变量：网络地位
        M_var = 'Emotional_Stability_score'  # 调节变量：情感稳定性
        Y_var = 'event_status'  # 因变量：留存状态

        # 检查变量是否存在
        if X_var not in self.X.columns or M_var not in self.X.columns:
            print(f"❌ 变量缺失: {X_var} 或 {M_var}")
            return None

        # 获取数据
        X = self.X[X_var].values
        M = self.X[M_var].values
        Y_original = self.y.values

        # 创建留存变量
        Y_retention = 1 - Y_original  # 现在1=留存, 0=流失

        # 中心化处理
        X_centered = X - np.mean(X)
        M_centered = M - np.mean(M)

        # 创建交互项
        XM_interaction = X_centered * M_centered

        # 构建回归模型
        predictors = np.column_stack([X_centered, M_centered, XM_interaction])

        # 逻辑回归
        model = LogisticRegression(max_iter=1000)
        model.fit(predictors, Y_retention)

        # 获取系数
        coef_X = model.coef_[0][0]      # 主效应X
        coef_M = model.coef_[0][1]      # 主效应M
        coef_XM = model.coef_[0][2]     # 交互效应

        # 简单斜率分析
        # 高情感稳定性 (+1SD)
        M_high = np.std(M)
        simple_slope_high = coef_X + coef_XM * M_high

        # 低情感稳定性 (-1SD)
        M_low = -np.std(M)
        simple_slope_low = coef_X + coef_XM * M_low

        # Bootstrap检验交互项系数
        bootstrap_interactions = []
        n_bootstrap = 5000
        n_samples = len(X)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot = X_centered[indices]
            M_boot = M_centered[indices]
            Y_boot = Y_retention[indices]
            XM_boot = X_boot * M_boot

            try:
                predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                model_boot = LogisticRegression(max_iter=1000)
                model_boot.fit(predictors_boot, Y_boot)
                bootstrap_interactions.append(model_boot.coef_[0][2])
            except:
                continue

        # 计算置信区间
        if bootstrap_interactions:
            ci_lower = np.percentile(bootstrap_interactions, 2.5)
            ci_upper = np.percentile(bootstrap_interactions, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 输出结果
        print(f"📈 网络地位调节分析结果:")
        print(f"   主效应 (degree_centrality): {coef_X:.4f}")
        print(f"   主效应 (Emotional_Stability): {coef_M:.4f}")
        print(f"   交互效应: {coef_XM:.4f}")
        print(f"   95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   低稳定性时的简单斜率: {simple_slope_low:.4f}")
        print(f"   高稳定性时的简单斜率: {simple_slope_high:.4f}")

        status = "✅ 显著调节" if significant else "❌ 无显著调节"
        print(f"   调节效应: {status}")

        if significant:
            if abs(simple_slope_low) > abs(simple_slope_high):
                interpretation = "✅ 假设得到支持：情感稳定性缓冲了网络地位的影响"
            else:
                interpretation = "❌ 假设未得到支持：情感稳定性未能缓冲网络地位影响"
            print(f"   💡 解释: {interpretation}")

        return {
            'main_effect_X': coef_X,
            'main_effect_M': coef_M,
            'interaction_effect': coef_XM,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant,
            'simple_slope_high': simple_slope_high,
            'simple_slope_low': simple_slope_low,
            'hypothesis_supported': significant and abs(simple_slope_low) > abs(simple_slope_high)
        }

    def moderation_analysis_received_comments(self):
        """路径D2: 社交验证的情感稳定性调节 (基于Stress and Coping Theory)"""
        print(f"\n🔍 路径D2: 社交验证的情感稳定性调节")
        print(f"💡 理论基础: Stress and Coping Theory (Lazarus & Folkman, 1984)")
        print(f"🎯 核心假设: 情感稳定性调节社交反馈对留存的影响强度")
        print("-" * 50)

        # 变量设置
        X_var = 'received_comments_count_log'  # 自变量：社交验证
        M_var = 'Emotional_Stability_score'  # 调节变量：情感稳定性
        Y_var = 'event_status'  # 因变量：留存状态

        # 检查变量是否存在
        if X_var not in self.X.columns or M_var not in self.X.columns:
            print(f"❌ 变量缺失: {X_var} 或 {M_var}")
            return None

        # 获取数据
        X = self.X[X_var].values
        M = self.X[M_var].values
        Y_original = self.y.values

        # 创建留存变量
        Y_retention = 1 - Y_original  # 现在1=留存, 0=流失

        # 中心化处理
        X_centered = X - np.mean(X)
        M_centered = M - np.mean(M)

        # 创建交互项
        XM_interaction = X_centered * M_centered

        # 构建回归模型
        predictors = np.column_stack([X_centered, M_centered, XM_interaction])

        # 逻辑回归
        model = LogisticRegression(max_iter=1000)
        model.fit(predictors, Y_retention)

        # 获取系数
        coef_X = model.coef_[0][0]      # 主效应X
        coef_M = model.coef_[0][1]      # 主效应M
        coef_XM = model.coef_[0][2]     # 交互效应

        # 简单斜率分析
        # 高情感稳定性 (+1SD)
        M_high = np.std(M)
        simple_slope_high = coef_X + coef_XM * M_high

        # 低情感稳定性 (-1SD)
        M_low = -np.std(M)
        simple_slope_low = coef_X + coef_XM * M_low

        # Bootstrap检验交互项系数
        bootstrap_interactions = []
        n_bootstrap = 5000
        n_samples = len(X)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot = X_centered[indices]
            M_boot = M_centered[indices]
            Y_boot = Y_retention[indices]
            XM_boot = X_boot * M_boot

            try:
                predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                model_boot = LogisticRegression(max_iter=1000)
                model_boot.fit(predictors_boot, Y_boot)
                bootstrap_interactions.append(model_boot.coef_[0][2])
            except:
                continue

        # 计算置信区间
        if bootstrap_interactions:
            ci_lower = np.percentile(bootstrap_interactions, 2.5)
            ci_upper = np.percentile(bootstrap_interactions, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 输出结果
        print(f"📈 社交验证调节分析结果:")
        print(f"   主效应 (received_comments_count_log): {coef_X:.4f}")
        print(f"   主效应 (Emotional_Stability): {coef_M:.4f}")
        print(f"   交互效应: {coef_XM:.4f}")
        print(f"   95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   低稳定性时的简单斜率: {simple_slope_low:.4f}")
        print(f"   高稳定性时的简单斜率: {simple_slope_high:.4f}")

        status = "✅ 显著调节" if significant else "❌ 无显著调节"
        print(f"   调节效应: {status}")

        if significant:
            if abs(simple_slope_low) > abs(simple_slope_high):
                interpretation = "✅ 假设得到支持：情感稳定性缓冲了社交反馈的影响"
            else:
                interpretation = "❌ 假设未得到支持：情感稳定性未能缓冲社交反馈影响"
            print(f"   💡 解释: {interpretation}")

        return {
            'main_effect_X': coef_X,
            'main_effect_M': coef_M,
            'interaction_effect': coef_XM,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant,
            'simple_slope_high': simple_slope_high,
            'simple_slope_low': simple_slope_low,
            'hypothesis_supported': significant and abs(simple_slope_low) > abs(simple_slope_high)
        }

    # 注意：moderation_analysis_has_comments 方法已删除
    # 因为与 moderation_analysis() 方法完全重复
    # has_received_comments × Emotional_Stability_score 的调节分析
    # 已在 moderation_analysis() 方法中完成

    def moderation_analysis_total_interactions(self):
        """路径D4: 行为投入的情感稳定性调节 (基于Self-Regulation Theory)"""
        print(f"\n🔍 路径D4: 行为投入的情感稳定性调节")
        print(f"💡 理论基础: Self-Regulation Theory (Baumeister & Heatherton, 1996)")
        print(f"🎯 核心假设: 情感稳定性调节过度参与的负面效应")
        print("-" * 50)

        # 变量设置
        X_var = 'total_interactions_log'  # 自变量：行为投入
        M_var = 'Emotional_Stability_score'  # 调节变量：情感稳定性
        Y_var = 'event_status'  # 因变量：留存状态

        # 检查变量是否存在
        if X_var not in self.X.columns or M_var not in self.X.columns:
            print(f"❌ 变量缺失: {X_var} 或 {M_var}")
            return None

        # 获取数据
        X = self.X[X_var].values
        M = self.X[M_var].values
        Y_original = self.y.values

        # 创建留存变量
        Y_retention = 1 - Y_original  # 现在1=留存, 0=流失

        # 中心化处理
        X_centered = X - np.mean(X)
        M_centered = M - np.mean(M)

        # 创建交互项
        XM_interaction = X_centered * M_centered

        # 构建回归模型
        predictors = np.column_stack([X_centered, M_centered, XM_interaction])

        # 逻辑回归
        model = LogisticRegression(max_iter=1000)
        model.fit(predictors, Y_retention)

        # 获取系数
        coef_X = model.coef_[0][0]      # 主效应X
        coef_M = model.coef_[0][1]      # 主效应M
        coef_XM = model.coef_[0][2]     # 交互效应

        # 简单斜率分析
        # 高情感稳定性 (+1SD)
        M_high = np.std(M)
        simple_slope_high = coef_X + coef_XM * M_high

        # 低情感稳定性 (-1SD)
        M_low = -np.std(M)
        simple_slope_low = coef_X + coef_XM * M_low

        # Bootstrap检验交互项系数
        bootstrap_interactions = []
        n_bootstrap = 5000
        n_samples = len(X)

        np.random.seed(42)
        for _ in range(n_bootstrap):
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_boot = X_centered[indices]
            M_boot = M_centered[indices]
            Y_boot = Y_retention[indices]
            XM_boot = X_boot * M_boot

            try:
                predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                model_boot = LogisticRegression(max_iter=1000)
                model_boot.fit(predictors_boot, Y_boot)
                bootstrap_interactions.append(model_boot.coef_[0][2])
            except:
                continue

        # 计算置信区间
        if bootstrap_interactions:
            ci_lower = np.percentile(bootstrap_interactions, 2.5)
            ci_upper = np.percentile(bootstrap_interactions, 97.5)
            significant = not (ci_lower <= 0 <= ci_upper)
        else:
            ci_lower = ci_upper = 0
            significant = False

        # 输出结果
        print(f"📈 行为投入调节分析结果:")
        print(f"   主效应 (total_interactions_log): {coef_X:.4f}")
        print(f"   主效应 (Emotional_Stability): {coef_M:.4f}")
        print(f"   交互效应: {coef_XM:.4f}")
        print(f"   95% CI: [{ci_lower:.4f}, {ci_upper:.4f}]")
        print(f"   低稳定性时的简单斜率: {simple_slope_low:.4f}")
        print(f"   高稳定性时的简单斜率: {simple_slope_high:.4f}")

        status = "✅ 显著调节" if significant else "❌ 无显著调节"
        print(f"   调节效应: {status}")

        if significant:
            if abs(simple_slope_low) > abs(simple_slope_high):
                interpretation = "✅ 假设得到支持：情感稳定性缓冲了过度参与的负面效应"
            else:
                interpretation = "❌ 假设未得到支持：情感稳定性未能缓冲过度参与效应"
            print(f"   💡 解释: {interpretation}")

        return {
            'main_effect_X': coef_X,
            'main_effect_M': coef_M,
            'interaction_effect': coef_XM,
            'ci_lower': ci_lower,
            'ci_upper': ci_upper,
            'significant': significant,
            'simple_slope_high': simple_slope_high,
            'simple_slope_low': simple_slope_low,
            'hypothesis_supported': significant and abs(simple_slope_low) > abs(simple_slope_high)
        }

    def systematic_emotional_stability_moderation(self):
        """系统性情感稳定性调节分析：测试所有S变量"""
        print(f"🔬 系统性测试所有S变量与Emotional_Stability_score的调节效应")

        # 所有S变量
        all_s_variables = [
            'total_interactions_log', 'has_received_comments', 'received_comments_count_log',
            'degree_centrality', 'pagerank', 'betweenness_centrality', 'closeness_centrality',
            'active_months', 'early_activity_log'
        ]

        results = {}
        significant_count = 0

        for s_var in all_s_variables:
            print(f"\n🔍 测试: {s_var} × Emotional_Stability_score")

            # 检查变量是否存在
            if s_var not in self.X.columns:
                print(f"❌ 变量缺失: {s_var}")
                continue

            # 获取数据
            X = self.X[s_var].values
            M = self.X['Emotional_Stability_score'].values
            Y_original = self.y.values
            Y_retention = 1 - Y_original

            # 中心化处理
            X_centered = X - np.mean(X)
            M_centered = M - np.mean(M)
            XM_interaction = X_centered * M_centered

            # 逻辑回归
            predictors = np.column_stack([X_centered, M_centered, XM_interaction])
            model = LogisticRegression(max_iter=1000)
            model.fit(predictors, Y_retention)

            # 获取系数
            coef_X = model.coef_[0][0]
            coef_M = model.coef_[0][1]
            coef_XM = model.coef_[0][2]

            # Bootstrap检验
            bootstrap_interactions = []
            n_bootstrap = 1000  # 减少Bootstrap次数以提高速度
            n_samples = len(X)

            np.random.seed(42)
            for _ in range(n_bootstrap):
                indices = np.random.choice(n_samples, n_samples, replace=True)
                X_boot = X_centered[indices]
                M_boot = M_centered[indices]
                Y_boot = Y_retention[indices]
                XM_boot = X_boot * M_boot

                try:
                    predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                    model_boot = LogisticRegression(max_iter=1000)
                    model_boot.fit(predictors_boot, Y_boot)
                    bootstrap_interactions.append(model_boot.coef_[0][2])
                except:
                    continue

            # 计算置信区间
            if bootstrap_interactions:
                ci_lower = np.percentile(bootstrap_interactions, 2.5)
                ci_upper = np.percentile(bootstrap_interactions, 97.5)
                significant = not (ci_lower <= 0 <= ci_upper)
            else:
                ci_lower = ci_upper = 0
                significant = False

            # 简单斜率分析
            M_high = np.std(M)
            M_low = -np.std(M)
            simple_slope_high = coef_X + coef_XM * M_high
            simple_slope_low = coef_X + coef_XM * M_low

            # 输出结果
            status = "✅ 显著" if significant else "❌ 不显著"
            print(f"   交互效应: {coef_XM:.4f}, CI: [{ci_lower:.4f}, {ci_upper:.4f}], {status}")

            if significant:
                significant_count += 1

            # 保存结果
            results[s_var] = {
                'main_effect_X': coef_X,
                'main_effect_M': coef_M,
                'interaction_effect': coef_XM,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'significant': significant,
                'simple_slope_high': simple_slope_high,
                'simple_slope_low': simple_slope_low
            }

        print(f"\n📊 情感稳定性调节总结: {significant_count}/{len(all_s_variables)} 个S变量显示显著调节效应")
        return results

    def systematic_social_efficacy_moderation(self):
        """系统性社交效能感调节分析：测试所有S变量"""
        print(f"🔬 系统性测试所有S变量与Social_Efficacy_score的调节效应")

        # 所有S变量
        all_s_variables = [
            'total_interactions_log', 'has_received_comments', 'received_comments_count_log',
            'degree_centrality', 'pagerank', 'betweenness_centrality', 'closeness_centrality',
            'active_months', 'early_activity_log'
        ]

        results = {}
        significant_count = 0

        for s_var in all_s_variables:
            print(f"\n🔍 测试: {s_var} × Social_Efficacy_score")

            # 检查变量是否存在
            if s_var not in self.X.columns:
                print(f"❌ 变量缺失: {s_var}")
                continue

            # 获取数据
            X = self.X[s_var].values
            M = self.X['Social_Efficacy_score'].values
            Y_original = self.y.values
            Y_retention = 1 - Y_original

            # 中心化处理
            X_centered = X - np.mean(X)
            M_centered = M - np.mean(M)
            XM_interaction = X_centered * M_centered

            # 逻辑回归
            predictors = np.column_stack([X_centered, M_centered, XM_interaction])
            model = LogisticRegression(max_iter=1000)
            model.fit(predictors, Y_retention)

            # 获取系数
            coef_X = model.coef_[0][0]
            coef_M = model.coef_[0][1]
            coef_XM = model.coef_[0][2]

            # Bootstrap检验
            bootstrap_interactions = []
            n_bootstrap = 1000  # 减少Bootstrap次数以提高速度
            n_samples = len(X)

            np.random.seed(42)
            for _ in range(n_bootstrap):
                indices = np.random.choice(n_samples, n_samples, replace=True)
                X_boot = X_centered[indices]
                M_boot = M_centered[indices]
                Y_boot = Y_retention[indices]
                XM_boot = X_boot * M_boot

                try:
                    predictors_boot = np.column_stack([X_boot, M_boot, XM_boot])
                    model_boot = LogisticRegression(max_iter=1000)
                    model_boot.fit(predictors_boot, Y_boot)
                    bootstrap_interactions.append(model_boot.coef_[0][2])
                except:
                    continue

            # 计算置信区间
            if bootstrap_interactions:
                ci_lower = np.percentile(bootstrap_interactions, 2.5)
                ci_upper = np.percentile(bootstrap_interactions, 97.5)
                significant = not (ci_lower <= 0 <= ci_upper)
            else:
                ci_lower = ci_upper = 0
                significant = False

            # 简单斜率分析
            M_high = np.std(M)
            M_low = -np.std(M)
            simple_slope_high = coef_X + coef_XM * M_high
            simple_slope_low = coef_X + coef_XM * M_low

            # 输出结果
            status = "✅ 显著" if significant else "❌ 不显著"
            print(f"   交互效应: {coef_XM:.4f}, CI: [{ci_lower:.4f}, {ci_upper:.4f}], {status}")

            if significant:
                significant_count += 1

            # 保存结果
            results[s_var] = {
                'main_effect_X': coef_X,
                'main_effect_M': coef_M,
                'interaction_effect': coef_XM,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'significant': significant,
                'simple_slope_high': simple_slope_high,
                'simple_slope_low': simple_slope_low
            }

        print(f"\n📊 社交效能感调节总结: {significant_count}/{len(all_s_variables)} 个S变量显示显著调节效应")
        return results

def main():
    """主函数：四个阈值的系统性分析"""
    print("🎯 开始四阈值SOR增强分析")
    print("📊 将分析90天、150天、180天、330天四个时间阈值")
    print("=" * 80)

    # 创建分析器
    analyzer = SOREnhancedAnalyzer(alpha=0.05, n_permutations=10000)

    # 存储所有阈值的结果
    all_threshold_results = {}

    # 逐个分析四个阈值
    for threshold_days in analyzer.threshold_days:
        print(f"\n🔍 开始分析 {threshold_days}天 阈值")
        print("=" * 60)

        # 加载对应的数据集
        dataset_file = analyzer.threshold_datasets[threshold_days]
        if not analyzer.load_data(dataset_file):
            print(f"❌ {threshold_days}天数据加载失败，跳过")
            continue

        # 执行完整分析
        print(f"🚀 执行 {threshold_days}天 完整分析")
        results = analyzer.comprehensive_analysis()
        model_results = analyzer.evaluate_model_performance()
        mechanism_results = analyzer.mediation_analysis()

        # 导出详细结果
        analyzer.export_detailed_results(results, model_results)

        # 保存结果
        all_threshold_results[threshold_days] = {
            'results': results,
            'model_results': model_results,
            'mechanism_results': mechanism_results
        }

        print(f"✅ {threshold_days}天 分析完成")

    print("\n🎉 四阈值SOR增强分析完成！")
    print("📁 生成文件:")
    print("   - SOR_enhanced_analysis_results_XXdays.xlsx (各阈值详细结果)")
    print("=" * 80)

    return analyzer, all_threshold_results

if __name__ == "__main__":
    analyzer, all_threshold_results = main()
