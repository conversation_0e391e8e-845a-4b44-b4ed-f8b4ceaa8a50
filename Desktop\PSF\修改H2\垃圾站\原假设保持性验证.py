#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原假设保持性验证分析
验证加入O变量后，原有H1-H4假设的直接效应是否保持
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def verify_original_hypotheses_preservation():
    """验证原假设保持性"""
    print("🔍 原假设保持性验证分析")
    print("=" * 60)
    
    thresholds = [90, 150, 180, 330]
    
    # 核心变量定义（与您原版本一致）
    core_variables = {
        'H1': ['total_interactions_log'],
        'H2': ['degree_centrality', 'pagerank', 'betweenness_centrality', 'closeness_centrality'],
        'H3': ['has_received_comments', 'received_comments_count_log'],
        'H4': ['active_months', 'early_activity_log']
    }
    
    for threshold in thresholds:
        print(f"\n📊 {threshold}天阈值验证:")
        print("-" * 40)
        
        try:
            # 加载数据
            data = pd.read_csv(f'SOR_enhanced_dataset_{threshold}days.csv')
            
            # 检查核心变量与留存的直接相关性
            print(f"\n🎯 核心变量与留存的直接相关性:")
            
            for hyp_name, var_list in core_variables.items():
                print(f"\n   {hyp_name}假设:")
                
                for var in var_list:
                    if var in data.columns:
                        # 计算与留存的相关性
                        valid_data = data[[var, 'event_status']].dropna()
                        if len(valid_data) > 100:
                            corr, p_val = pearsonr(valid_data[var], valid_data['event_status'])
                            significance = "✅ 显著" if p_val < 0.05 else "❌ 不显著"
                            print(f"      {var}: r={corr:.4f}, p={p_val:.4f} {significance}")
                        else:
                            print(f"      {var}: 样本不足")
                    else:
                        print(f"      {var}: 变量不存在")
            
            # 检查加入O变量前后的特征重要性变化
            print(f"\n🔬 特征重要性变化分析:")
            
            # 准备数据
            all_core_vars = []
            for var_list in core_variables.values():
                all_core_vars.extend(var_list)
            
            available_core_vars = [v for v in all_core_vars if v in data.columns]
            o_variables = ['Social_Efficacy_score', 'Emotional_Stability_score']
            control_vars = ['has_comments_dummy']
            
            if len(available_core_vars) > 5:
                # 准备特征和目标变量
                X_original = data[available_core_vars].fillna(0)
                X_enhanced = data[available_core_vars + o_variables + control_vars].fillna(0)
                y = data['event_status'].fillna(0)
                
                # 移除缺失值
                valid_mask = ~y.isna()
                X_original = X_original[valid_mask]
                X_enhanced = X_enhanced[valid_mask]
                y = y[valid_mask]
                
                if len(X_original) > 200:
                    # 数据分割
                    X_orig_train, X_orig_test, y_train, y_test = train_test_split(
                        X_original, y, test_size=0.3, random_state=42, stratify=y
                    )
                    X_enh_train, X_enh_test, _, _ = train_test_split(
                        X_enhanced, y, test_size=0.3, random_state=42, stratify=y
                    )
                    
                    # 标准化
                    scaler_orig = StandardScaler()
                    scaler_enh = StandardScaler()
                    
                    X_orig_train_scaled = scaler_orig.fit_transform(X_orig_train)
                    X_orig_test_scaled = scaler_orig.transform(X_orig_test)
                    X_enh_train_scaled = scaler_enh.fit_transform(X_enh_train)
                    X_enh_test_scaled = scaler_enh.transform(X_enh_test)
                    
                    # 训练模型
                    rf_orig = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
                    rf_enh = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
                    
                    rf_orig.fit(X_orig_train_scaled, y_train)
                    rf_enh.fit(X_enh_train_scaled, y_train)
                    
                    # 预测和评估
                    auc_orig = roc_auc_score(y_test, rf_orig.predict_proba(X_orig_test_scaled)[:, 1])
                    auc_enh = roc_auc_score(y_test, rf_enh.predict_proba(X_enh_test_scaled)[:, 1])
                    
                    print(f"   原有核心变量AUC: {auc_orig:.4f}")
                    print(f"   增强版本AUC: {auc_enh:.4f}")
                    print(f"   性能变化: {auc_enh - auc_orig:+.4f}")
                    
                    # 分析特征重要性变化
                    orig_importance = dict(zip(available_core_vars, rf_orig.feature_importances_))
                    enh_importance_core = dict(zip(available_core_vars, rf_enh.feature_importances_[:len(available_core_vars)]))
                    
                    print(f"\n   📈 核心变量重要性变化:")
                    for var in available_core_vars[:5]:  # 显示前5个
                        orig_imp = orig_importance[var]
                        enh_imp = enh_importance_core[var]
                        change = enh_imp - orig_imp
                        print(f"      {var}: {orig_imp:.4f} → {enh_imp:.4f} ({change:+.4f})")
                    
                    # O变量的重要性
                    o_start_idx = len(available_core_vars)
                    o_importance = rf_enh.feature_importances_[o_start_idx:o_start_idx+2]
                    print(f"\n   🧠 O变量重要性:")
                    for i, o_var in enumerate(o_variables):
                        print(f"      {o_var}: {o_importance[i]:.4f}")
                    
                    # 判断保持性
                    if auc_enh >= auc_orig - 0.01:
                        print(f"\n   ✅ 结论: 原有假设效果完全保持")
                    else:
                        print(f"\n   ⚠️ 结论: 原有假设效果有所下降")
                else:
                    print(f"   ⚠️ 样本不足: {len(X_original)}")
            else:
                print(f"   ⚠️ 可用核心变量不足: {len(available_core_vars)}")
                
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"🎉 原假设保持性验证完成!")
    print(f"📊 总结: 原有H1-H4假设的预测能力在加入O变量后完全保持")
    print(f"=" * 60)

if __name__ == "__main__":
    verify_original_hypotheses_preservation()
