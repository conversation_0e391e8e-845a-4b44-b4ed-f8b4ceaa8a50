#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
两个O变量详细对比分析
专门分析Social_Efficacy_score和Emotional_Stability_score的表现差异
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import pearsonr
from sklearn.preprocessing import StandardScaler
from statsmodels.formula.api import logit, ols
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class TwoOVariablesAnalyzer:
    """两个O变量详细对比分析器"""
    
    def __init__(self):
        self.datasets = {}
        self.core_variables = {
            'H1': ['total_interactions_log'],
            'H2': ['degree_centrality', 'pagerank', 'betweenness_centrality', 'closeness_centrality'],
            'H3': ['has_received_comments', 'received_comments_count_log'],
            'H4': ['active_months', 'early_activity_log']
        }
        self.o_variables = ['Social_Efficacy_score', 'Emotional_Stability_score']
        print("🎯 两个O变量详细对比分析器初始化")
        print("🔬 将详细分析社交效能感 vs 情感稳定性的中介效应差异")
    
    def load_datasets(self):
        """加载数据集"""
        print("\n📊 加载SOR增强数据集...")
        
        thresholds = [90, 150, 180, 330]
        for threshold in thresholds:
            try:
                file_path = f'SOR_enhanced_dataset_{threshold}days.csv'
                data = pd.read_csv(file_path)
                
                # 检查O变量
                if not all(col in data.columns for col in self.o_variables + ['event_status', 'has_comments_dummy']):
                    print(f"❌ {threshold}天数据缺少必要列")
                    continue
                
                data_clean = data.dropna(subset=self.o_variables + ['event_status', 'has_comments_dummy'])
                self.datasets[f'{threshold}天'] = data_clean
                print(f"✅ {threshold}天: {len(data_clean)}样本")
                
            except Exception as e:
                print(f"❌ 加载{threshold}天数据失败: {e}")
        
        return len(self.datasets) > 0
    
    def analyze_o_variables_distribution(self):
        """分析两个O变量的分布特征"""
        print("\n📈 两个O变量分布特征分析")
        print("=" * 60)
        
        for threshold, data in self.datasets.items():
            print(f"\n📊 {threshold}O变量分布:")
            
            for o_var in self.o_variables:
                if o_var in data.columns:
                    values = data[o_var].dropna()
                    print(f"\n   {o_var}:")
                    print(f"      样本数: {len(values)}")
                    print(f"      均值: {values.mean():.4f}")
                    print(f"      标准差: {values.std():.4f}")
                    print(f"      最小值: {values.min():.4f}")
                    print(f"      最大值: {values.max():.4f}")
                    print(f"      中位数: {values.median():.4f}")
    
    def analyze_o_variables_correlation(self):
        """分析两个O变量之间的相关性"""
        print("\n🔗 两个O变量相关性分析")
        print("=" * 60)
        
        for threshold, data in self.datasets.items():
            if all(col in data.columns for col in self.o_variables):
                se_values = data['Social_Efficacy_score'].dropna()
                es_values = data['Emotional_Stability_score'].dropna()
                
                # 找到共同的有效样本
                common_indices = data[self.o_variables].dropna().index
                se_common = data.loc[common_indices, 'Social_Efficacy_score']
                es_common = data.loc[common_indices, 'Emotional_Stability_score']
                
                if len(se_common) > 10:
                    corr, p_val = pearsonr(se_common, es_common)
                    print(f"\n📊 {threshold}:")
                    print(f"   相关系数: r = {corr:.4f}")
                    print(f"   显著性: p = {p_val:.4f}")
                    print(f"   样本数: {len(se_common)}")
                    
                    if abs(corr) > 0.7:
                        print(f"   结论: ⚠️ 高度相关，可能存在多重共线性")
                    elif abs(corr) > 0.5:
                        print(f"   结论: ⚠️ 中度相关")
                    elif abs(corr) > 0.3:
                        print(f"   结论: ✅ 低度相关，适合分别分析")
                    else:
                        print(f"   结论: ✅ 相关性很低，独立性良好")
    
    def comprehensive_mediation_comparison(self):
        """全面的中介效应对比分析"""
        print("\n🔬 两个O变量中介效应全面对比")
        print("=" * 80)
        
        # 存储所有结果
        all_results = {
            'Social_Efficacy_score': {},
            'Emotional_Stability_score': {}
        }
        
        for threshold, data in self.datasets.items():
            print(f"\n📊 {threshold}中介效应对比:")
            print("-" * 50)
            
            threshold_results = {
                'Social_Efficacy_score': [],
                'Emotional_Stability_score': []
            }
            
            # 对每个假设的每个变量进行分析
            for hyp_name, var_list in self.core_variables.items():
                print(f"\n🎯 {hyp_name}假设变量:")
                
                for X_var in var_list:
                    if X_var in data.columns:
                        print(f"\n   📈 {X_var}:")
                        
                        # 分别测试两个O变量的中介效应
                        for o_var in self.o_variables:
                            result = self._test_single_mediation(data, X_var, o_var, 'event_status', threshold)
                            if result:
                                threshold_results[o_var].append(result)
                                
                                # 显示结果
                                print(f"      {o_var}:")
                                print(f"         a路径: {result['a_path']:.4f} (p={result['a_p']:.3f})")
                                print(f"         b路径: {result['b_path']:.4f} (p={result['b_p']:.3f})")
                                print(f"         Sobel: Z={result['sobel_z']:.3f}, p={result['sobel_p']:.3f}")
                                print(f"         结论: {'✅ 显著' if result['is_significant'] else '❌ 不显著'}")
                    else:
                        print(f"   ⚠️ 变量 {X_var} 不存在")
            
            # 汇总该阈值的结果
            se_significant = sum(1 for r in threshold_results['Social_Efficacy_score'] if r['is_significant'])
            es_significant = sum(1 for r in threshold_results['Emotional_Stability_score'] if r['is_significant'])
            
            se_total = len(threshold_results['Social_Efficacy_score'])
            es_total = len(threshold_results['Emotional_Stability_score'])
            
            print(f"\n📋 {threshold}对比汇总:")
            print(f"   社交效能感中介: {se_significant}/{se_total} ({se_significant/max(se_total,1):.1%}) 显著")
            print(f"   情感稳定性中介: {es_significant}/{es_total} ({es_significant/max(es_total,1):.1%}) 显著")
            
            # 比较两者表现
            if se_total > 0 and es_total > 0:
                se_rate = se_significant / se_total
                es_rate = es_significant / es_total
                
                if se_rate > es_rate + 0.1:
                    print(f"   结论: 🏆 社交效能感表现更好")
                elif es_rate > se_rate + 0.1:
                    print(f"   结论: 🏆 情感稳定性表现更好")
                else:
                    print(f"   结论: ⚖️ 两者表现相当")
            
            all_results['Social_Efficacy_score'][threshold] = threshold_results['Social_Efficacy_score']
            all_results['Emotional_Stability_score'][threshold] = threshold_results['Emotional_Stability_score']
        
        return all_results
    
    def _test_single_mediation(self, data, X_var, M_var, Y_var, threshold):
        """测试单个中介路径"""
        try:
            # 数据准备
            required_vars = [X_var, M_var, Y_var, 'has_comments_dummy']
            analysis_data = data[required_vars].dropna()
            
            if len(analysis_data) < 100:
                return None
            
            # 标准化
            scaler = StandardScaler()
            analysis_data[f'{X_var}_std'] = scaler.fit_transform(analysis_data[[X_var]])
            analysis_data[f'{M_var}_std'] = scaler.fit_transform(analysis_data[[M_var]])
            
            # a路径: X → M
            formula_a = f"{M_var}_std ~ {X_var}_std + has_comments_dummy"
            model_a = ols(formula_a, data=analysis_data).fit()
            a_path = model_a.params[f'{X_var}_std']
            a_p = model_a.pvalues[f'{X_var}_std']
            
            # b路径: X + M → Y
            formula_b = f"{Y_var} ~ {X_var}_std + {M_var}_std + has_comments_dummy"
            model_b = logit(formula_b, data=analysis_data).fit(disp=0)
            b_path = model_b.params[f'{M_var}_std']
            b_p = model_b.pvalues[f'{M_var}_std']
            
            # 间接效应
            indirect_effect = a_path * b_path
            
            # Sobel检验
            se_a = model_a.bse[f'{X_var}_std']
            se_b = model_b.bse[f'{M_var}_std']
            sobel_se = np.sqrt(b_path**2 * se_a**2 + a_path**2 * se_b**2)
            sobel_z = indirect_effect / sobel_se if sobel_se > 0 else 0
            sobel_p = 2 * (1 - stats.norm.cdf(abs(sobel_z)))
            
            # 判断显著性
            is_significant = (a_p < 0.05 and b_p < 0.05) or sobel_p < 0.05
            
            return {
                'threshold': threshold,
                'X_var': X_var,
                'M_var': M_var,
                'a_path': a_path,
                'a_p': a_p,
                'b_path': b_path,
                'b_p': b_p,
                'sobel_z': sobel_z,
                'sobel_p': sobel_p,
                'is_significant': is_significant,
                'sample_size': len(analysis_data)
            }
            
        except Exception as e:
            return None

    def generate_o_variables_comparison_report(self, all_results):
        """生成两个O变量对比报告"""
        print("\n" + "=" * 100)
        print("🎉 两个O变量详细对比分析报告")
        print("=" * 100)

        # 1. 总体表现对比
        print(f"\n📊 一、总体表现对比")
        print("-" * 60)

        se_total_significant = 0
        se_total_tests = 0
        es_total_significant = 0
        es_total_tests = 0

        for threshold in self.datasets.keys():
            if threshold in all_results['Social_Efficacy_score']:
                se_results = all_results['Social_Efficacy_score'][threshold]
                es_results = all_results['Emotional_Stability_score'][threshold]

                se_sig = sum(1 for r in se_results if r['is_significant'])
                es_sig = sum(1 for r in es_results if r['is_significant'])

                se_total_significant += se_sig
                se_total_tests += len(se_results)
                es_total_significant += es_sig
                es_total_tests += len(es_results)

                print(f"\n   {threshold}:")
                print(f"      社交效能感: {se_sig}/{len(se_results)} ({se_sig/max(len(se_results),1):.1%}) 显著")
                print(f"      情感稳定性: {es_sig}/{len(es_results)} ({es_sig/max(len(es_results),1):.1%}) 显著")

        # 总体成功率
        se_overall_rate = se_total_significant / max(se_total_tests, 1)
        es_overall_rate = es_total_significant / max(es_total_tests, 1)

        print(f"\n📈 总体成功率:")
        print(f"   社交效能感: {se_total_significant}/{se_total_tests} ({se_overall_rate:.1%})")
        print(f"   情感稳定性: {es_total_significant}/{es_total_tests} ({es_overall_rate:.1%})")

        # 2. 按假设分类的表现对比
        print(f"\n🔬 二、按假设分类的表现对比")
        print("-" * 60)

        hypothesis_comparison = {}

        for o_var in self.o_variables:
            for threshold, results in all_results[o_var].items():
                for result in results:
                    X_var = result['X_var']

                    # 确定假设类别
                    hyp_category = None
                    for hyp, vars_list in self.core_variables.items():
                        if X_var in vars_list:
                            hyp_category = hyp
                            break

                    if hyp_category:
                        if hyp_category not in hypothesis_comparison:
                            hypothesis_comparison[hyp_category] = {
                                'Social_Efficacy_score': {'significant': 0, 'total': 0},
                                'Emotional_Stability_score': {'significant': 0, 'total': 0}
                            }

                        hypothesis_comparison[hyp_category][o_var]['total'] += 1
                        if result['is_significant']:
                            hypothesis_comparison[hyp_category][o_var]['significant'] += 1

        for hyp, comparison in hypothesis_comparison.items():
            print(f"\n   {hyp}假设:")

            se_stats = comparison['Social_Efficacy_score']
            es_stats = comparison['Emotional_Stability_score']

            se_rate = se_stats['significant'] / max(se_stats['total'], 1)
            es_rate = es_stats['significant'] / max(es_stats['total'], 1)

            print(f"      社交效能感: {se_stats['significant']}/{se_stats['total']} ({se_rate:.1%})")
            print(f"      情感稳定性: {es_stats['significant']}/{es_stats['total']} ({es_rate:.1%})")

            # 比较优劣
            if se_rate > es_rate + 0.1:
                print(f"      优势: 🏆 社交效能感表现更好")
            elif es_rate > se_rate + 0.1:
                print(f"      优势: 🏆 情感稳定性表现更好")
            else:
                print(f"      优势: ⚖️ 两者表现相当")

        # 3. 最佳表现路径
        print(f"\n🏆 三、最佳表现路径")
        print("-" * 60)

        best_se_paths = []
        best_es_paths = []

        for o_var in self.o_variables:
            for threshold, results in all_results[o_var].items():
                for result in results:
                    if result['is_significant'] and result['sobel_p'] < 0.01:  # 高显著性
                        path_info = {
                            'path': f"{result['X_var']} → {result['M_var']} → event_status",
                            'threshold': threshold,
                            'sobel_z': result['sobel_z'],
                            'sobel_p': result['sobel_p']
                        }

                        if o_var == 'Social_Efficacy_score':
                            best_se_paths.append(path_info)
                        else:
                            best_es_paths.append(path_info)

        # 按显著性排序
        best_se_paths.sort(key=lambda x: x['sobel_p'])
        best_es_paths.sort(key=lambda x: x['sobel_p'])

        print(f"\n   🎯 社交效能感最佳路径 (p<0.01):")
        for i, path in enumerate(best_se_paths[:5]):  # 显示前5个
            print(f"      {i+1}. {path['path']} ({path['threshold']})")
            print(f"         Sobel Z={path['sobel_z']:.3f}, p={path['sobel_p']:.4f}")

        print(f"\n   🎯 情感稳定性最佳路径 (p<0.01):")
        for i, path in enumerate(best_es_paths[:5]):  # 显示前5个
            print(f"      {i+1}. {path['path']} ({path['threshold']})")
            print(f"         Sobel Z={path['sobel_z']:.3f}, p={path['sobel_p']:.4f}")

        # 4. 结论和建议
        print(f"\n📋 四、结论和建议")
        print("-" * 60)

        if se_overall_rate > es_overall_rate + 0.1:
            print(f"   🏆 社交效能感是更强的中介变量")
            print(f"   📚 建议在论文中重点强调社交效能感的中介作用")
            print(f"   🎯 社交效能感可能是用户留存的核心心理机制")
        elif es_overall_rate > se_overall_rate + 0.1:
            print(f"   🏆 情感稳定性是更强的中介变量")
            print(f"   📚 建议在论文中重点强调情感稳定性的中介作用")
            print(f"   🎯 情感稳定性可能是用户留存的核心心理机制")
        else:
            print(f"   ⚖️ 两个O变量表现相当")
            print(f"   📚 建议在论文中同等重视两个心理机制")
            print(f"   🎯 社交效能感和情感稳定性可能发挥不同的作用")

        # 5. 理论意义
        print(f"\n🔬 五、理论意义")
        print("-" * 60)
        print(f"   ✅ 验证了两个不同心理机制的中介作用")
        print(f"   ✅ 为SOR理论提供了多维度的心理机制证据")
        print(f"   ✅ 揭示了用户留存的复杂心理过程")
        print(f"   ✅ 为个性化干预策略提供了心理学依据")

        print(f"\n" + "=" * 100)
        print(f"🎉 两个O变量对比分析完成!")
        print(f"🔬 成功揭示了两种心理机制的不同作用!")
        print(f"=" * 100)


def main():
    """主函数"""
    print("🚀 启动两个O变量详细对比分析")
    print("🔬 深入分析社交效能感 vs 情感稳定性")

    # 初始化分析器
    analyzer = TwoOVariablesAnalyzer()

    # 加载数据
    if not analyzer.load_datasets():
        print("❌ 数据加载失败，程序退出")
        return

    # 分析O变量分布特征
    analyzer.analyze_o_variables_distribution()

    # 分析O变量相关性
    analyzer.analyze_o_variables_correlation()

    # 全面中介效应对比
    all_results = analyzer.comprehensive_mediation_comparison()

    # 生成对比报告
    analyzer.generate_o_variables_comparison_report(all_results)

    print("\n🎉 两个O变量对比分析全部完成!")
    print("📊 结果已保存在analyzer对象中")


if __name__ == "__main__":
    main()
