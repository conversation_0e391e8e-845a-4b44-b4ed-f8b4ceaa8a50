#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复180天数据集的格式问题

作者: AI助手
日期: 2025-01-11
版本: 1.0
"""

import pandas as pd
import os
import re

def fix_180days_dataset():
    """修复180天数据集的格式问题"""

    input_file = r"C:\Users\<USER>\Desktop\PSF\修改H2\user_survival_analysis_dataset_180days_cleaned.csv"
    output_file = r"C:\Users\<USER>\Desktop\PSF\修改H2\user_survival_analysis_dataset_180days_cleaned_fixed.csv"

    print("🔧 修复180天数据集格式问题")
    print(f"📂 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")

    try:
        # 先尝试用其他编码读取
        encodings = ['utf-8', 'gbk', 'latin-1']
        content = None

        for encoding in encodings:
            try:
                with open(input_file, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"✅ 使用 {encoding} 编码成功读取文件")
                break
            except:
                continue

        if content is None:
            print("❌ 无法读取文件")
            return False

        print(f"📊 原始文件大小: {len(content)} 字符")

        # 使用更强力的修复方法
        lines = content.split('\n')
        print(f"📊 原始行数: {len(lines)}")

        # 读取第一行作为表头，确定列数
        header = lines[0]
        expected_cols = len(header.split(','))
        print(f"📊 期望列数: {expected_cols}")

        fixed_lines = [header]

        for i, line in enumerate(lines[1:], 1):
            if not line.strip():
                continue

            # 计算当前行的列数
            cols = len(line.split(','))

            if cols == expected_cols:
                # 正常行
                fixed_lines.append(line)
            elif cols > expected_cols:
                # 可能是合并行，尝试分割
                print(f"🔧 修复第 {i+1} 行: {cols} 列 -> 期望 {expected_cols} 列")

                # 寻找用户ID模式 (7位数字开头)
                parts = line.split(',')

                # 找到第二个用户ID的位置
                second_uid_pos = None
                for j in range(expected_cols, len(parts)):
                    if re.match(r'^\d{7}$', parts[j]):
                        second_uid_pos = j
                        break

                if second_uid_pos:
                    # 分割成两行
                    first_row = ','.join(parts[:second_uid_pos])
                    second_row = ','.join(parts[second_uid_pos:])

                    fixed_lines.append(first_row)
                    fixed_lines.append(second_row)
                    print(f"   分割为两行: {len(first_row.split(','))} + {len(second_row.split(','))} 列")
                else:
                    # 无法分割，截断到期望列数
                    truncated = ','.join(parts[:expected_cols])
                    fixed_lines.append(truncated)
                    print(f"   截断到 {expected_cols} 列")
            else:
                # 列数不足，可能是不完整的行
                print(f"⚠️ 第 {i+1} 行列数不足: {cols} < {expected_cols}")
                fixed_lines.append(line)

        # 重新组合内容
        fixed_content = '\n'.join(fixed_lines)
        print(f"📊 修复后行数: {len(fixed_lines)}")

        # 保存修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)

        print("✅ 文件修复完成")

        # 验证修复结果
        try:
            df = pd.read_csv(output_file)
            print(f"✅ 修复验证成功: {df.shape}")
            print(f"   用户数: {df['uid'].nunique()}")

            # 备份原文件并替换
            backup_file = input_file.replace('.csv', '_backup.csv')
            if os.path.exists(input_file):
                if os.path.exists(backup_file):
                    os.remove(backup_file)
                os.rename(input_file, backup_file)
            os.rename(output_file, input_file)

            print(f"📋 原文件已备份为: {backup_file}")
            print(f"✅ 修复文件已替换原文件")

            return True

        except Exception as e:
            print(f"❌ 修复验证失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 文件修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 180天数据集修复工具")
    print("="*50)
    
    success = fix_180days_dataset()
    
    if success:
        print("\n🎉 180天数据集修复成功!")
        print("现在可以重新运行O变量计算了")
    else:
        print("\n❌ 180天数据集修复失败")

if __name__ == "__main__":
    main()
