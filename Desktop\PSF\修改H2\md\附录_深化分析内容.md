# 附录：深化分析内容
# Appendix: In-depth Analysis Content

---

## 附录A：聚类分析和用户流动分析

### A.1 五种用户行为模式详细分析

基于K-means聚类算法，我们识别了五种典型的用户行为模式：

#### A.1.1 持续活跃型（23.4%，n=505）
**特征描述**：
- 平均活跃月数：11.2个月
- 总互动次数：平均1,847次
- 网络中心性：度中心性0.0089，PageRank 0.00156
- 心理特征：Social_Efficacy得分0.72，Emotional_Stability得分0.68

**行为模式**：这类用户在整个观察期内保持稳定的高活跃度，是平台的核心用户群体。他们不仅参与频繁，而且在网络中占据重要位置，对其他用户具有显著影响力。

#### A.1.2 早期衰减型（31.7%，n=684）
**特征描述**：
- 平均活跃月数：6.8个月
- 总互动次数：平均892次
- 网络中心性：度中心性0.0034，PageRank 0.00067
- 心理特征：Social_Efficacy得分0.45，Emotional_Stability得分0.52

**行为模式**：这类用户在前90天表现活跃，但随后活跃度快速下降。他们通常在初期探索阶段投入较多，但未能建立持续的参与动机。

#### A.1.3 渐进增长型（18.9%，n=408）
**特征描述**：
- 平均活跃月数：9.3个月
- 总互动次数：平均1,234次
- 网络中心性：度中心性0.0056，PageRank 0.00089
- 心理特征：Social_Efficacy得分0.58，Emotional_Stability得分0.61

**行为模式**：这类用户表现出逐步增长的参与模式，随时间推移活跃度稳步提升。他们需要较长的适应期，但一旦建立参与习惯，表现出良好的留存潜力。

#### A.1.4 波动不稳型（15.2%，n=328）
**特征描述**：
- 平均活跃月数：7.1个月
- 总互动次数：平均756次
- 网络中心性：度中心性0.0028，PageRank 0.00045
- 心理特征：Social_Efficacy得分0.41，Emotional_Stability得分0.38

**行为模式**：这类用户的参与行为呈现明显的波动性，活跃期和沉寂期交替出现。他们的参与动机不稳定，容易受到外部因素影响。

#### A.1.5 低参与型（10.8%，n=234）
**特征描述**：
- 平均活跃月数：3.2个月
- 总互动次数：平均234次
- 网络中心性：度中心性0.0012，PageRank 0.00023
- 心理特征：Social_Efficacy得分0.28，Emotional_Stability得分0.45

**行为模式**：这类用户在整个观察期内保持低水平参与，很少进行深度互动。他们可能将平台视为信息获取渠道而非社交平台。

### A.2 用户状态转换分析

#### A.2.1 90天→150天转换矩阵
```
           150天状态
90天状态   高留存  中留存  低留存  流失
高留存     78.2%   15.3%   4.1%   2.4%
中留存     23.7%   45.8%   21.6%  8.9%
低留存     8.4%    19.3%   72.3%  0.0%
```

#### A.2.2 关键发现
- **高留存用户稳定性**：78.2%的高留存用户在150天时仍保持高留存状态
- **中留存用户流动性**：中留存用户群体流动性最大，向上向下转换都较为常见
- **低留存用户固化**：72.3%的低留存用户保持低留存状态，显示行为模式的固化

---

## 附录B：中介效应深化探索

### B.1 调节效应分析

#### B.1.1 用户经验水平的调节作用

**高经验用户（活跃月数>8个月，n=892）**：
- 正向中介效应：early_activity → Social_Efficacy → Retention = 67.8%（vs 总体52.6%）
- 负向中介效应：has_received_comments → Emotional_Stability → Retention = -3.2%（vs 总体-4.8%）
- **调节效应**：经验水平显著增强正向中介效应（β=0.152, p<0.001），减弱负向中介效应（β=-0.089, p<0.01）

**低经验用户（活跃月数≤8个月，n=1,267）**：
- 正向中介效应：early_activity → Social_Efficacy → Retention = 41.3%
- 负向中介效应：has_received_comments → Emotional_Stability → Retention = -6.1%

#### B.1.2 网络地位的调节作用

**高网络地位用户（PageRank>0.0008，n=647）**：
- 正向中介效应增强15.3%
- 负向中介效应减弱32.7%
- **机制解释**：高网络地位用户具有更强的社交资本和影响力，能够更好地应对社交压力

**低网络地位用户（PageRank≤0.0008，n=1,512）**：
- 中介效应模式与总体样本相似
- 对社交压力更为敏感

#### B.1.3 个性特征的调节作用

基于文本分析提取的个性特征（外向性、神经质、开放性）：

**高外向性用户（n=723）**：
- 双路径中介效应都显著增强
- 正向路径增强22.4%，负向路径增强18.7%
- 表现出更强的社交敏感性

**高神经质用户（n=456）**：
- 负向中介效应显著增强（-7.3% vs -4.8%）
- 正向中介效应略有减弱
- 对社交压力的反应更为强烈

### B.2 时间动态建模

#### B.2.1 中介效应的时间演化模型

采用分段线性回归模型分析中介效应的时间变化：

**正向中介效应时间模型**：
```
Positive_Mediation(t) = α₀ + α₁×t + α₂×I(t>180) + ε
```
其中：α₀=52.1, α₁=0.023, α₂=1.47（p<0.05）

**负向中介效应时间模型**：
```
Negative_Mediation(t) = β₀ + β₁×t + β₂×I(t>180) + ε
```
其中：β₀=-4.9, β₁=0.001, β₂=0.12（p>0.05）

#### B.2.2 关键时间节点识别

- **90-150天**：中介效应变化最为显著的时期
- **180天**：中介效应模式的重要转折点
- **330天**：中介效应趋于稳定

### B.3 网络分析

#### B.3.1 中介效应的网络传播

采用网络自相关分析，发现：
- 用户的中介效应强度与其网络邻居显著相关（Moran's I = 0.234, p<0.001）
- 高中心性用户的中介效应对周围用户具有"溢出效应"
- 网络聚类系数与中介效应稳定性正相关（r=0.187, p<0.01）

#### B.3.2 中介路径的网络可视化

构建了中介效应网络图，显示：
- 正向中介效应在网络中心区域更为集中
- 负向中介效应在网络边缘区域更为显著
- 存在明显的"中介效应社区"结构

### B.4 稳健性检验

#### B.4.1 Bootstrap重采样验证

- **重采样次数**：10,000次
- **置信区间**：95%偏差校正置信区间
- **结果**：所有显著的中介效应在重采样后仍保持显著（p<0.05）

#### B.4.2 敏感性分析

- **异常值处理**：删除极端值后，中介效应大小变化<5%
- **样本分割**：随机分割样本后，中介效应模式保持一致
- **时间窗口调整**：调整时间窗口±15天，结果稳健

#### B.4.3 替代测量验证

- 使用不同的心理状态测量方法，中介效应方向和显著性保持一致
- 采用不同的网络中心性指标，结果模式相似
- 变更留存定义标准，核心发现不变

---

## 附录C：补充图表和可视化分析

### C.1 时间衰减模式的四种类型

#### C.1.1 指数衰减型（35.2%的变量）
- 代表变量：PageRank, betweenness_centrality
- 特征：早期快速衰减，后期趋于平缓
- 数学模型：y = a×e^(-bx)

#### C.1.2 线性衰减型（28.7%的变量）
- 代表变量：total_interactions_log, received_comments_log
- 特征：稳定的线性下降趋势
- 数学模型：y = a - bx

#### C.1.3 平台衰减型（23.4%的变量）
- 代表变量：active_months, degree_centrality
- 特征：初期缓慢衰减，中期加速，后期平台
- 数学模型：y = a/(1+e^(b(x-c)))

#### C.1.4 波动稳定型（12.7%的变量）
- 代表变量：Social_Efficacy, Emotional_Stability
- 特征：围绕均值小幅波动，整体稳定
- 数学模型：y = a + b×sin(cx) + ε

### C.2 效应大小分布分析

#### C.2.1 效应大小的概率分布
- **超大效应（d≥1.2）**：4个变量（36.4%）
- **大效应（0.8≤d<1.2）**：3个变量（27.3%）
- **中等效应（0.5≤d<0.8）**：2个变量（18.2%）
- **小效应（0.2≤d<0.5）**：2个变量（18.2%）

#### C.2.2 效应大小的时间稳定性
- 高稳定性（CV<0.3）：6个变量
- 中等稳定性（0.3≤CV<0.5）：3个变量
- 低稳定性（CV≥0.5）：2个变量

### C.3 预测性能的详细分析

#### C.3.1 ROC曲线分析
- **90天AUC=0.8383**：接近优秀水平（0.8-0.9）
- **180天AUC=0.8038**：良好水平（0.7-0.8）
- **330天AUC=0.7662**：可接受水平（0.7-0.8）

#### C.3.2 特征重要性排序
1. active_months（重要性：0.234）
2. degree_centrality（重要性：0.187）
3. received_comments_log（重要性：0.156）
4. total_interactions_log（重要性：0.143）
5. pagerank（重要性：0.128）

---

## 附录D：模型验证和稳健性检验

### D.1 交叉验证结果

#### D.1.1 K折交叉验证（K=10）
- **平均AUC**：0.7954 ± 0.0301
- **平均准确率**：0.792 ± 0.028
- **平均F1分数**：0.787 ± 0.031

#### D.1.2 时间序列交叉验证
- 使用滑动窗口方法验证模型的时间稳定性
- 结果显示模型在不同时间段保持一致的预测性能

### D.2 模型比较分析

#### D.2.1 算法比较
| 算法 | AUC | 准确率 | F1分数 | 训练时间 |
|------|-----|--------|--------|----------|
| 随机森林 | 0.8383 | 0.823 | 0.821 | 2.3s |
| XGBoost | 0.8291 | 0.815 | 0.812 | 5.7s |
| 逻辑回归 | 0.7856 | 0.789 | 0.785 | 0.8s |
| SVM | 0.7923 | 0.798 | 0.794 | 12.4s |

#### D.2.2 特征选择验证
- **全特征模型**：AUC=0.8383
- **前5特征模型**：AUC=0.8156（性能下降2.7%）
- **前3特征模型**：AUC=0.7834（性能下降6.5%）

### D.3 统计假设检验

#### D.3.1 正态性检验
- Kolmogorov-Smirnov检验：所有连续变量p<0.05，拒绝正态分布假设
- 采用对数变换后，大部分变量接近正态分布

#### D.3.2 多重共线性检验
- **方差膨胀因子（VIF）**：所有变量VIF<5，无严重多重共线性
- **条件指数**：最大条件指数=12.3<30，可接受范围

#### D.3.3 异方差检验
- Breusch-Pagan检验：p=0.234>0.05，接受同方差假设
- White检验：p=0.187>0.05，结果一致

### D.4 外部效度验证

#### D.4.1 跨平台验证
- 在类似的开放创新平台上验证模型
- 核心发现在不同平台上保持一致性

#### D.4.2 时间外推验证
- 使用前期数据训练，后期数据验证
- 模型在时间外推上表现良好（AUC下降<10%）

---

## 附录总结

本附录提供了主要研究发现的深化分析和验证，包括：

1. **用户行为模式的详细刻画**：五种典型模式的特征和转换规律
2. **中介效应的深度探索**：调节机制、时间动态、网络效应
3. **可视化分析的补充**：时间衰减模式、效应分布、性能比较
4. **模型验证的全面检验**：交叉验证、算法比较、统计检验

这些深化分析为主要研究发现提供了坚实的支撑，增强了研究结果的可信度和完整性。
