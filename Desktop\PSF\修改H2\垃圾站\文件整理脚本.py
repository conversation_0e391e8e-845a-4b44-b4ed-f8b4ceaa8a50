#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值考虑文件夹整理脚本
将散落的阈值相关文件分类整理到指定文件夹

作者：研究团队
日期：2025年1月
"""

import os
import shutil
from pathlib import Path

def organize_threshold_files():
    """整理阈值相关文件"""
    
    # 定义路径
    base_dir = Path(r"C:\Users\<USER>\Desktop\PSF\修改H2")
    threshold_dir = base_dir / "阈值考虑"
    
    # 创建子文件夹
    subdirs = {
        "01_分析脚本": "Python分析脚本",
        "02_生成图表": "生成的图表文件", 
        "03_分析报告": "分析报告和文档",
        "04_原始数据": "相关的原始数据文件"
    }
    
    for subdir, desc in subdirs.items():
        subdir_path = threshold_dir / subdir
        subdir_path.mkdir(exist_ok=True)
        print(f"✅ 创建文件夹: {subdir} - {desc}")
    
    # 定义文件移动规则
    file_rules = {
        # 图表文件
        "02_生成图表": [
            "时间阈值选择的数据驱动分析.png",
            "阈值选择的科学依据分析.png", 
            "四阈值关键指标详细分析.png",
            "变量分布特征分析.png",
            "变量预测能力分析.png"
        ],
        
        # 分析脚本（已经在阈值考虑文件夹中的不需要移动）
        "01_分析脚本": [],
        
        # 分析报告
        "03_分析报告": [],
        
        # 原始数据
        "04_原始数据": []
    }
    
    # 移动文件
    moved_files = []
    
    for category, files in file_rules.items():
        target_dir = threshold_dir / category
        
        for filename in files:
            source_file = base_dir / filename
            target_file = target_dir / filename
            
            if source_file.exists():
                try:
                    shutil.move(str(source_file), str(target_file))
                    moved_files.append(f"{filename} -> {category}")
                    print(f"📁 移动: {filename} -> {category}")
                except Exception as e:
                    print(f"❌ 移动失败: {filename} - {e}")
    
    # 整理现有的脚本文件
    script_files = [
        "时间阈值选择的数据驱动分析.py",
        "阈值选择可视化分析.py", 
        "四阈值关键指标分析.py",
        "变量选择的数据驱动分析.py",
        "运行所有分析.py"
    ]
    
    scripts_dir = threshold_dir / "01_分析脚本"
    for script in script_files:
        source = threshold_dir / script
        target = scripts_dir / script
        if source.exists() and not target.exists():
            try:
                shutil.move(str(source), str(target))
                moved_files.append(f"{script} -> 01_分析脚本")
                print(f"📁 整理脚本: {script} -> 01_分析脚本")
            except Exception as e:
                print(f"❌ 整理失败: {script} - {e}")
    
    # 整理文档文件
    doc_files = [
        "README.md",
        "从数据角度解释变量选择.md",
        "分析结果总结.md"
    ]
    
    docs_dir = threshold_dir / "03_分析报告"
    for doc in doc_files:
        source = threshold_dir / doc
        target = docs_dir / doc
        if source.exists() and not target.exists():
            try:
                shutil.move(str(source), str(target))
                moved_files.append(f"{doc} -> 03_分析报告")
                print(f"📁 整理文档: {doc} -> 03_分析报告")
            except Exception as e:
                print(f"❌ 整理失败: {doc} - {e}")
    
    return moved_files

def create_index_file():
    """创建文件夹索引"""
    
    threshold_dir = Path(r"C:\Users\<USER>\Desktop\PSF\修改H2\阈值考虑")
    index_file = threshold_dir / "文件夹索引.md"
    
    content = """# 阈值考虑文件夹索引

## 📁 文件夹结构

### 01_分析脚本
**用途**: 存放所有Python分析脚本
**文件**:
- `时间阈值选择的数据驱动分析.py` - 主要数据分析脚本
- `阈值选择可视化分析.py` - 可视化图表生成脚本
- `四阈值关键指标分析.py` - 关键指标详细分析脚本
- `变量选择的数据驱动分析.py` - 变量选择分析脚本
- `运行所有分析.py` - 一键运行所有分析的脚本

### 02_生成图表
**用途**: 存放所有生成的图表文件
**文件**:
- `时间阈值选择的数据驱动分析.png` - 主要分析图表
- `阈值选择的科学依据分析.png` - 科学依据分析图表
- `四阈值关键指标详细分析.png` - 关键指标详细分析图表（**PPT推荐**）
- `变量分布特征分析.png` - 变量分布特征图表
- `变量预测能力分析.png` - 变量预测能力图表

### 03_分析报告
**用途**: 存放分析报告和文档
**文件**:
- `README.md` - 项目总体说明文档
- `从数据角度解释变量选择.md` - 变量选择的数据驱动解释
- `分析结果总结.md` - 完整的分析结果总结

### 04_原始数据
**用途**: 存放相关的原始数据文件（如有需要）

## 🎯 使用指南

### 快速开始
1. 运行 `01_分析脚本/运行所有分析.py` 执行完整分析
2. 查看 `02_生成图表/` 中的图表结果
3. 阅读 `03_分析报告/` 中的详细报告

### PPT制作建议
- **首选图表**: `02_生成图表/四阈值关键指标详细分析.png`
- **备选图表**: `02_生成图表/时间阈值选择的数据驱动分析.png`

### 学术写作参考
- 参考 `03_分析报告/分析结果总结.md` 获取完整的分析结论
- 使用 `03_分析报告/从数据角度解释变量选择.md` 了解变量选择依据

## 📊 核心发现摘要

### 四个时间阈值的数据表现
- **90天**: 留存率100.0%, 用户适应期基准
- **150天**: 留存率86.1%, 首次大规模流失(13.9%)
- **180天**: 留存率81.9%, 承诺决策分水岭
- **330天**: 留存率75.7%, 长期稳定确认

### 选择依据
1. **阶梯式递减**: 留存率呈现清晰的递减趋势
2. **用户分化**: 分化程度从0.000递增到0.088
3. **行为转换**: 每个阈值对应独特的行为模式转换
4. **预测价值**: 组合使用实现最佳预测效果

---
*最后更新: 2025年1月*
"""
    
    with open(index_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"📋 创建索引文件: {index_file}")

def main():
    """主函数"""
    print("🚀 开始整理阈值考虑文件夹...")
    
    # 整理文件
    moved_files = organize_threshold_files()
    
    # 创建索引
    create_index_file()
    
    # 输出结果
    print(f"\n✅ 整理完成!")
    print(f"📁 共移动/整理了 {len(moved_files)} 个文件")
    
    if moved_files:
        print(f"\n📋 文件移动记录:")
        for record in moved_files:
            print(f"   • {record}")
    
    print(f"\n📂 文件夹结构:")
    print(f"   阈值考虑/")
    print(f"   ├── 01_分析脚本/     (Python脚本)")
    print(f"   ├── 02_生成图表/     (PNG图表文件)")
    print(f"   ├── 03_分析报告/     (Markdown文档)")
    print(f"   ├── 04_原始数据/     (数据文件)")
    print(f"   └── 文件夹索引.md    (索引文档)")
    
    print(f"\n🎯 推荐使用:")
    print(f"   • PPT制作: 02_生成图表/四阈值关键指标详细分析.png")
    print(f"   • 学术写作: 03_分析报告/分析结果总结.md")
    print(f"   • 代码运行: 01_分析脚本/运行所有分析.py")

if __name__ == "__main__":
    main()
