#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
假设印证验证系统
==============

专门用于验证原始研究假设的科学验证框架
基于完美统计分析系统，针对性验证四个核心假设

作者：AI助手
日期：2025-01-21
"""

import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score

from scipy import stats
from scipy.stats import pearsonr, spearmanr
from statsmodels.stats.multitest import multipletests
from statsmodels.stats.power import ttest_power

import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import os

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

class HypothesisValidationSystem:
    """假设印证验证系统"""
    
    def __init__(self, alpha=0.05):
        """初始化验证系统"""
        self.alpha = alpha
        
        # 定义你的四个核心假设
        self.core_hypotheses = {
            'H1_social_interaction': {
                'name': '社交互动频率假设',
                'description': '社交互动频率对用户持续参与具有正向影响',
                'features': ['total_interactions', 'avg_monthly_interactions', 'total_posts', 'total_comments_made'],
                'expected_direction': 'positive',
                'theoretical_basis': '社会学习理论 + 自我决定理论'
            },
            
            'H2_network_centrality': {
                'name': '多维度网络中心性假设',
                'description': '多维度网络中心性对用户持续参与具有正向影响',
                'features': ['in_degree_centrality', 'out_degree_centrality', 'betweenness_centrality', 
                           'closeness_centrality', 'eigenvector_centrality', 'clustering_coefficient'],
                'expected_direction': 'positive',
                'theoretical_basis': '社会资本组合理论'
            },
            
            'H3_feedback_existence': {
                'name': '反馈存在性假设',
                'description': '反馈存在性对用户持续参与具有正向影响',
                'features': ['has_received_comments', 'received_comments_count'],
                'expected_direction': 'positive',
                'theoretical_basis': '霍耐特社会承认理论'
            },
            
            'H4_early_activity': {
                'name': '早期活跃度双重效应假设',
                'description': '早期活跃度对用户持续参与关系呈现双重效应特征',
                'features': ['early_activity_level', 'first_month_posts', 'initial_engagement'],
                'expected_direction': 'curvilinear',
                'theoretical_basis': '投资模型理论 + 工作要求-资源模型'
            }
        }
        
        print("🔬 假设印证验证系统已初始化")
        print("🎯 目标：科学验证四个核心研究假设")

    def load_and_prepare_data(self, filename):
        """加载数据并准备假设验证"""
        print(f"\n📁 加载数据进行假设验证...")
        
        try:
            df = pd.read_csv(filename)
            print(f"   ✅ 数据加载成功: {df.shape}")
            
            # 准备特征
            exclude_cols = ['uid', 'registeredTime', 'last_actual_activity_time', 
                           'event_status', 'tenure_days']
            
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            X = df[feature_cols].fillna(0)
            y = df['event_status'].astype(int)
            
            return X, y, feature_cols, df
            
        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return None, None, None, None

    def validate_hypothesis_H1(self, X, y, feature_names):
        """验证H1: 社交互动频率假设"""
        print(f"\n🔬 验证H1: 社交互动频率假设")
        print("-"*50)
        
        h1_features = self.core_hypotheses['H1_social_interaction']['features']
        available_h1_features = []
        
        # 查找可用的H1相关特征
        for feature in h1_features:
            matches = [f for f in feature_names if feature.lower() in f.lower()]
            available_h1_features.extend(matches)
        
        # 添加对数变换特征
        log_features = [f for f in feature_names if 'log' in f.lower() and 
                       any(base in f.lower() for base in ['interaction', 'post', 'comment'])]
        available_h1_features.extend(log_features)
        
        available_h1_features = list(set(available_h1_features))  # 去重
        
        print(f"   🎯 H1相关特征 ({len(available_h1_features)}个):")
        for feature in available_h1_features:
            print(f"      - {feature}")
        
        if not available_h1_features:
            print("   ❌ 未找到H1相关特征")
            return None
        
        # 对每个H1特征进行严格检验
        h1_results = {}
        p_values = []
        
        for feature in available_h1_features:
            if feature in feature_names:
                feature_idx = feature_names.index(feature)
                feature_data = X.iloc[:, feature_idx]
                
                # 置换检验
                observed_corr = abs(pearsonr(feature_data, y)[0])
                
                # 1000次置换
                null_distribution = []
                np.random.seed(42)
                for _ in range(1000):
                    y_permuted = np.random.permutation(y)
                    null_corr = abs(pearsonr(feature_data, y_permuted)[0])
                    null_distribution.append(null_corr)
                
                p_value = np.mean(np.array(null_distribution) >= observed_corr)
                p_values.append(p_value)
                
                # 效应大小
                group1 = feature_data[y == 1]
                group0 = feature_data[y == 0]
                
                if len(group1) > 1 and len(group0) > 1:
                    pooled_std = np.sqrt(
                        ((len(group1) - 1) * np.var(group1, ddof=1) +
                         (len(group0) - 1) * np.var(group0, ddof=1)) /
                        (len(group1) + len(group0) - 2)
                    )
                    cohens_d = (np.mean(group1) - np.mean(group0)) / pooled_std if pooled_std > 0 else 0
                else:
                    cohens_d = 0
                
                h1_results[feature] = {
                    'correlation': observed_corr,
                    'p_value': p_value,
                    'cohens_d': cohens_d,
                    'direction': 'positive' if np.mean(group1) > np.mean(group0) else 'negative'
                }
        
        # 多重比较校正
        if p_values:
            rejected, p_corrected, _, _ = multipletests(p_values, alpha=self.alpha, method='bonferroni')
            
            significant_count = np.sum(rejected)
            total_count = len(p_values)
            
            print(f"\n   📊 H1验证结果:")
            print(f"      总特征数: {total_count}")
            print(f"      显著特征数: {significant_count}")
            print(f"      显著比例: {significant_count/total_count:.1%}")
            
            # 详细结果
            print(f"\n   🏆 H1显著特征:")
            for i, (feature, result) in enumerate(h1_results.items()):
                if rejected[i]:
                    direction_symbol = "↑" if result['direction'] == 'positive' else "↓"
                    print(f"      ✅ {feature}: p={result['p_value']:.6f}, d={result['cohens_d']:.3f} {direction_symbol}")
            
            # H1假设结论
            if significant_count > 0:
                positive_significant = sum(1 for i, (_, result) in enumerate(h1_results.items()) 
                                         if rejected[i] and result['direction'] == 'positive')
                
                if positive_significant >= significant_count * 0.8:  # 80%以上为正向
                    h1_conclusion = "✅ H1假设得到强支持：社交互动频率确实对用户留存有正向影响"
                else:
                    h1_conclusion = "⚠️ H1假设部分支持：社交互动有影响，但方向性复杂"
            else:
                h1_conclusion = "❌ H1假设未得到支持：社交互动频率影响不显著"
            
            print(f"\n   🎯 H1结论: {h1_conclusion}")
            
            return {
                'hypothesis': 'H1_social_interaction',
                'total_features': total_count,
                'significant_features': significant_count,
                'significance_rate': significant_count/total_count,
                'conclusion': h1_conclusion,
                'detailed_results': h1_results,
                'support_level': 'strong' if significant_count >= total_count * 0.5 else 
                               'moderate' if significant_count >= total_count * 0.3 else 'weak'
            }
        
        return None

    def validate_hypothesis_H2(self, X, y, feature_names):
        """验证H2: 多维度网络中心性假设"""
        print(f"\n🔬 验证H2: 多维度网络中心性假设")
        print("-"*50)
        
        h2_features = self.core_hypotheses['H2_network_centrality']['features']
        available_h2_features = []
        
        # 查找网络中心性特征
        centrality_keywords = ['centrality', 'clustering', 'pagerank', 'degree']
        for keyword in centrality_keywords:
            matches = [f for f in feature_names if keyword.lower() in f.lower()]
            available_h2_features.extend(matches)
        
        available_h2_features = list(set(available_h2_features))
        
        print(f"   🎯 H2相关特征 ({len(available_h2_features)}个):")
        for feature in available_h2_features:
            print(f"      - {feature}")
        
        if not available_h2_features:
            print("   ❌ 未找到H2相关特征")
            return None
        
        # 验证逻辑同H1，但重点关注网络中心性的组合效应
        h2_results = self._conduct_feature_validation(available_h2_features, X, y, feature_names, 'H2')
        
        return h2_results

    def validate_hypothesis_H3(self, X, y, feature_names):
        """验证H3: 反馈存在性假设"""
        print(f"\n🔬 验证H3: 反馈存在性假设")
        print("-"*50)
        
        # 查找反馈相关特征
        feedback_keywords = ['comment', 'feedback', 'received', 'response']
        available_h3_features = []
        
        for keyword in feedback_keywords:
            matches = [f for f in feature_names if keyword.lower() in f.lower()]
            available_h3_features.extend(matches)
        
        # 特别关注二元特征（存在性）
        binary_features = [f for f in feature_names if f.startswith('has_')]
        available_h3_features.extend(binary_features)
        
        available_h3_features = list(set(available_h3_features))
        
        print(f"   🎯 H3相关特征 ({len(available_h3_features)}个):")
        for feature in available_h3_features:
            print(f"      - {feature}")
        
        if not available_h3_features:
            print("   ❌ 未找到H3相关特征")
            return None
        
        h3_results = self._conduct_feature_validation(available_h3_features, X, y, feature_names, 'H3')
        
        return h3_results

    def validate_hypothesis_H4(self, X, y, feature_names):
        """验证H4: 早期活跃度双重效应假设"""
        print(f"\n🔬 验证H4: 早期活跃度双重效应假设")
        print("-"*50)
        
        # 查找早期活跃度特征
        early_keywords = ['early', 'first', 'initial', 'month']
        available_h4_features = []
        
        for keyword in early_keywords:
            matches = [f for f in feature_names if keyword.lower() in f.lower()]
            available_h4_features.extend(matches)
        
        available_h4_features = list(set(available_h4_features))
        
        print(f"   🎯 H4相关特征 ({len(available_h4_features)}个):")
        for feature in available_h4_features:
            print(f"      - {feature}")
        
        if not available_h4_features:
            print("   ❌ 未找到H4相关特征")
            return None
        
        # H4需要特殊处理：检验非线性关系
        h4_results = self._conduct_nonlinear_validation(available_h4_features, X, y, feature_names)
        
        return h4_results

    def _conduct_feature_validation(self, features, X, y, feature_names, hypothesis_id):
        """通用特征验证方法"""
        results = {}
        p_values = []
        
        for feature in features:
            if feature in feature_names:
                feature_idx = feature_names.index(feature)
                feature_data = X.iloc[:, feature_idx]
                
                # 置换检验
                observed_corr = abs(pearsonr(feature_data, y)[0])
                
                null_distribution = []
                np.random.seed(42)
                for _ in range(1000):
                    y_permuted = np.random.permutation(y)
                    null_corr = abs(pearsonr(feature_data, y_permuted)[0])
                    null_distribution.append(null_corr)
                
                p_value = np.mean(np.array(null_distribution) >= observed_corr)
                p_values.append(p_value)
                
                # 效应大小
                group1 = feature_data[y == 1]
                group0 = feature_data[y == 0]
                
                if len(group1) > 1 and len(group0) > 1:
                    pooled_std = np.sqrt(
                        ((len(group1) - 1) * np.var(group1, ddof=1) +
                         (len(group0) - 1) * np.var(group0, ddof=1)) /
                        (len(group1) + len(group0) - 2)
                    )
                    cohens_d = (np.mean(group1) - np.mean(group0)) / pooled_std if pooled_std > 0 else 0
                else:
                    cohens_d = 0
                
                results[feature] = {
                    'correlation': observed_corr,
                    'p_value': p_value,
                    'cohens_d': cohens_d,
                    'direction': 'positive' if np.mean(group1) > np.mean(group0) else 'negative'
                }
        
        # 多重比较校正和结论
        if p_values:
            rejected, p_corrected, _, _ = multipletests(p_values, alpha=self.alpha, method='bonferroni')
            
            significant_count = np.sum(rejected)
            total_count = len(p_values)
            
            print(f"\n   📊 {hypothesis_id}验证结果:")
            print(f"      总特征数: {total_count}")
            print(f"      显著特征数: {significant_count}")
            print(f"      显著比例: {significant_count/total_count:.1%}")
            
            return {
                'hypothesis': hypothesis_id,
                'total_features': total_count,
                'significant_features': significant_count,
                'significance_rate': significant_count/total_count,
                'detailed_results': results,
                'support_level': 'strong' if significant_count >= total_count * 0.5 else 
                               'moderate' if significant_count >= total_count * 0.3 else 'weak'
            }
        
        return None

    def _conduct_nonlinear_validation(self, features, X, y, feature_names):
        """H4的非线性关系验证"""
        print("   🔄 检验双重效应（非线性关系）...")
        
        # 这里需要更复杂的非线性检验
        # 简化版本：检验二次项的显著性
        
        results = {}
        for feature in features:
            if feature in feature_names:
                feature_idx = feature_names.index(feature)
                feature_data = X.iloc[:, feature_idx]
                
                # 线性相关
                linear_corr = pearsonr(feature_data, y)[0]
                
                # 二次项相关（检验非线性）
                feature_squared = feature_data ** 2
                quadratic_corr = pearsonr(feature_squared, y)[0]
                
                results[feature] = {
                    'linear_correlation': linear_corr,
                    'quadratic_correlation': quadratic_corr,
                    'nonlinear_evidence': abs(quadratic_corr) > abs(linear_corr) * 0.5
                }
        
        return {
            'hypothesis': 'H4',
            'detailed_results': results,
            'support_level': 'exploratory'  # 需要更复杂的分析
        }

    def run_comprehensive_hypothesis_validation(self, filename):
        """运行完整的假设验证"""
        print("🚀 启动假设印证验证系统")
        print("="*60)
        
        # 加载数据
        X, y, feature_names, df = self.load_and_prepare_data(filename)
        if X is None:
            return None
        
        # 验证四个假设
        validation_results = {}
        
        validation_results['H1'] = self.validate_hypothesis_H1(X, y, feature_names)
        validation_results['H2'] = self.validate_hypothesis_H2(X, y, feature_names)
        validation_results['H3'] = self.validate_hypothesis_H3(X, y, feature_names)
        validation_results['H4'] = self.validate_hypothesis_H4(X, y, feature_names)
        
        # 生成综合报告
        self.generate_hypothesis_validation_report(validation_results)
        
        return validation_results

    def generate_hypothesis_validation_report(self, results):
        """生成假设验证报告"""
        print(f"\n📄 假设验证综合报告")
        print("="*60)
        
        for hypothesis_id, result in results.items():
            if result:
                print(f"\n🔬 {hypothesis_id}:")
                print(f"   假设名称: {self.core_hypotheses.get(hypothesis_id, {}).get('name', 'Unknown')}")
                print(f"   支持程度: {result.get('support_level', 'unknown')}")
                if 'significance_rate' in result:
                    print(f"   显著性比例: {result['significance_rate']:.1%}")
                if 'conclusion' in result:
                    print(f"   结论: {result['conclusion']}")
        
        print(f"\n🎯 总体评估:")
        supported_hypotheses = sum(1 for r in results.values() 
                                 if r and r.get('support_level') in ['strong', 'moderate'])
        total_hypotheses = len([r for r in results.values() if r])
        
        if total_hypotheses > 0:
            support_rate = supported_hypotheses / total_hypotheses
            print(f"   假设支持率: {supported_hypotheses}/{total_hypotheses} ({support_rate:.1%})")
            
            if support_rate >= 0.75:
                print("   🏆 理论框架得到强支持！")
            elif support_rate >= 0.5:
                print("   ✅ 理论框架得到中等支持")
            else:
                print("   ⚠️ 理论框架需要修正")


if __name__ == "__main__":
    # 创建假设验证系统
    validator = HypothesisValidationSystem()
    
    # 查找数据文件
    data_files = [
        'user_survival_analysis_dataset_90days_cleaned.csv',
        'user_survival_analysis_dataset_150days_cleaned.csv',
        'user_survival_analysis_dataset_180days_cleaned.csv',
        'user_survival_analysis_dataset_330days_cleaned.csv'
    ]
    
    # 运行验证
    for filename in data_files:
        if os.path.exists(filename):
            print(f"🎯 使用数据文件: {filename}")
            results = validator.run_comprehensive_hypothesis_validation(filename)
            break
    else:
        print("❌ 未找到数据文件")
