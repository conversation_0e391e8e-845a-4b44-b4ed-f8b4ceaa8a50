#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版cntext测试 - 只测试核心情感分析功能
避免复杂依赖问题
"""

def test_basic_cntext():
    """测试cntext基本功能"""
    try:
        # 只导入stats模块，避免其他复杂依赖
        from cntext.stats import dict_pkl_list, load_pkl_dict, sentiment
        print("✅ cntext核心模块导入成功")
        
        # 测试获取词典列表
        dicts = dict_pkl_list()
        print(f"📚 可用词典数量: {len(dicts)}")
        print("前10个词典:")
        for i, d in enumerate(dicts[:10], 1):
            print(f"  {i:2d}. {d}")
        
        return True
        
    except Exception as e:
        print(f"❌ cntext导入失败: {e}")
        return False

def test_dutir_dict():
    """测试DUTIR情感词典"""
    try:
        from cntext.stats import load_pkl_dict, sentiment
        
        print("\n🎭 测试DUTIR情感词典...")
        
        # 加载DUTIR词典
        dutir_data = load_pkl_dict('DUTIR.pkl')
        dutir_dict = dutir_data['DUTIR']
        
        print(f"情感类别: {list(dutir_dict.keys())}")
        
        # 显示每个类别的词汇数量
        for emotion, words in dutir_dict.items():
            print(f"{emotion}: {len(words)}个词汇, 示例: {words[:5]}")
        
        return dutir_dict
        
    except Exception as e:
        print(f"❌ DUTIR词典测试失败: {e}")
        return None

def test_sentiment_analysis():
    """测试情感分析"""
    try:
        from cntext.stats import load_pkl_dict, sentiment
        
        print("\n💭 测试情感分析...")
        
        # 加载词典
        dutir_dict = load_pkl_dict('DUTIR.pkl')['DUTIR']
        
        # 测试文本
        test_texts = [
            "我今天很开心，学到了新知识",
            "这个社区氛围很好，大家都很友善",
            "我对结果很失望，感觉很沮丧",
            "我们团队合作得很棒，每个人都很积极"
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n测试 {i}: {text}")
            
            result = sentiment(text=text, diction=dutir_dict, lang='chinese')
            
            # 找出主要情感
            emotions = ['哀', '好', '惊', '惧', '乐', '怒', '恶']
            emotion_scores = {e: result.get(f'{e}_num', 0) for e in emotions}
            
            print(f"情感分数: {emotion_scores}")
            
            # 找出最高分情感
            max_emotion = max(emotion_scores, key=emotion_scores.get)
            max_score = emotion_scores[max_emotion]
            
            if max_score > 0:
                print(f"主要情感: {max_emotion} (分数: {max_score})")
            else:
                print("主要情感: 中性")
        
        return True
        
    except Exception as e:
        print(f"❌ 情感分析测试失败: {e}")
        return False

def create_o_variable_dict():
    """创建O变量词典"""
    try:
        from cntext.stats import load_pkl_dict
        
        print("\n🎯 创建O变量词典...")
        
        # 加载基础词典
        dutir_dict = load_pkl_dict('DUTIR.pkl')['DUTIR']
        
        # 设计O变量词典
        o_variables = {
            'social_efficacy': {
                'description': '社交效能感',
                'positive_words': dutir_dict['好'][:20],
                'negative_words': dutir_dict['惧'][:20],
                'confidence_words': ['确定', '肯定', '相信', '自信', '成功']
            },
            
            'community_belonging': {
                'description': '社区归属感',
                'collective_words': ['我们', '大家', '一起', '共同', '团队'],
                'belonging_words': ['归属', '融入', '接纳', '认同', '家'],
                'support_words': ['支持', '帮助', '关心', '理解', '鼓励']
            },
            
            'emotional_satisfaction': {
                'description': '情感满足度',
                'joy_words': dutir_dict['乐'][:20],
                'satisfaction_words': ['满意', '满足', '开心', '愉快', '舒适']
            },
            
            'cognitive_engagement': {
                'description': '认知参与度',
                'thinking_words': ['思考', '分析', '理解', '学习', '研究'],
                'learning_words': ['掌握', '提升', '进步', '成长', '收获']
            }
        }
        
        print("O变量词典创建成功！")
        for var_name, var_data in o_variables.items():
            print(f"- {var_name}: {var_data['description']}")
        
        # 保存到文件
        import json
        import os

        # 确保目录存在
        os.makedirs('O类变量/词典', exist_ok=True)

        with open('O类变量/词典/O变量词典.json', 'w', encoding='utf-8') as f:
            json.dump(o_variables, f, ensure_ascii=False, indent=2)
        
        print("✅ O变量词典已保存到文件")
        
        return o_variables
        
    except Exception as e:
        print(f"❌ O变量词典创建失败: {e}")
        return None

def test_o_variable_analysis():
    """测试O变量分析"""
    try:
        print("\n📊 测试O变量分析...")
        
        # 加载O变量词典
        import json
        with open('O类变量/词典/O变量词典.json', 'r', encoding='utf-8') as f:
            o_variables = json.load(f)
        
        # 测试文本
        test_text = "我们团队今天学习了新知识，大家都很开心，我觉得很有收获，对这个社区很满意"
        
        print(f"测试文本: {test_text}")
        
        # 分析每个O变量
        results = {}
        for var_name, var_data in o_variables.items():
            var_scores = {}
            
            for category, words in var_data.items():
                if category != 'description' and isinstance(words, list):
                    # 计算词汇出现次数
                    count = sum(1 for word in words if word in test_text)
                    var_scores[category] = count
            
            results[var_name] = var_scores
            
            # 计算总分
            total_score = sum(var_scores.values())
            print(f"\n{var_name} ({var_data['description']}): 总分 {total_score}")
            for category, score in var_scores.items():
                if score > 0:
                    print(f"  - {category}: {score}")
        
        return results
        
    except Exception as e:
        print(f"❌ O变量分析测试失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 简化版cntext测试开始")
    print("="*50)
    
    # 1. 测试基本功能
    if not test_basic_cntext():
        print("❌ 基本功能测试失败，退出")
        return
    
    # 2. 测试DUTIR词典
    dutir_dict = test_dutir_dict()
    if dutir_dict is None:
        print("❌ DUTIR词典测试失败，退出")
        return
    
    # 3. 测试情感分析
    if not test_sentiment_analysis():
        print("❌ 情感分析测试失败")
        return
    
    # 4. 创建O变量词典
    o_variables = create_o_variable_dict()
    if o_variables is None:
        print("❌ O变量词典创建失败")
        return
    
    # 5. 测试O变量分析
    results = test_o_variable_analysis()
    if results is None:
        print("❌ O变量分析测试失败")
        return
    
    print("\n" + "="*50)
    print("🎉 所有测试完成！")
    print("✅ cntext基本功能正常")
    print("✅ DUTIR情感词典可用")
    print("✅ O变量词典已创建")
    print("✅ O变量分析功能正常")
    
    print("\n📋 下一步建议:")
    print("1. 将O变量分析集成到四阈值分析框架")
    print("2. 批量处理用户评论数据")
    print("3. 验证S→O→R的中介效应")

if __name__ == "__main__":
    main()
