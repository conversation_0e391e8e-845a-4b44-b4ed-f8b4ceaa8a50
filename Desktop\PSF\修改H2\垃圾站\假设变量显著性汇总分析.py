#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
假设变量显著性汇总分析
基于四阈值科学分析报告的详细变量显著性分析
"""

import pandas as pd
import numpy as np

# 论文理论变量定义
PAPER_THEORETICAL_VARIABLES = {
    'H1_social_interaction': {
        'name': '社交互动频率假设',
        'theory_vars': [
            'total_interactions_log',  # 核心理论变量
            'total_posts_log',         # 发帖行为
            'total_comments_made_log'  # 评论行为
        ],
        'description': '用户在社区中的总互动次数，包括发帖数量和评论数量的总和'
    },
    'H2_network_centrality': {
        'name': '多维度网络中心性假设',
        'theory_vars': [
            'in_degree_centrality',      # 声望资本
            'out_degree_centrality',     # 影响力资本
            'degree_centrality',         # 连接资本
            'betweenness_centrality',    # 桥梁资本
            'closeness_centrality',      # 通路资本
            'eigenvector_centrality'     # 派生影响力资本
        ],
        'description': '六种网络中心性指标构成的多维度社会资本组合'
    },
    'H3_feedback_existence': {
        'name': '反馈存在性假设',
        'theory_vars': [
            'has_received_comments',     # 核心理论变量
            'received_comments_count'    # 反馈数量
        ],
        'description': '反馈存在性的二元变量测量，检验"存在性胜过质量"假设'
    },
    'H4_early_activity': {
        'name': '早期活跃度假设',
        'theory_vars': [
            'early_activity_log',        # 核心理论变量
            'active_months'              # 活跃持续性
        ],
        'description': '用户在注册后前30天内的活动强度'
    }
}

# 实验实际变量（基于四阈值分析结果）
EXPERIMENT_ACTUAL_VARIABLES = {
    'H1_social_interaction': [
        'any_strong_negative_comment',
        'any_strong_positive_comment', 
        'avg_monthly_interactions',
        'avg_monthly_interactions_log',
        'comments_made_L30D',
        'comments_made_L90D',
        'has_received_comments',  # 注意：这个被错误分配到H1
        'in_degree_as_post_author',
        'in_degree_as_post_author_log',
        'interaction_trend_L30D_vs_avg',
        'interaction_trend_L30D_vs_early',
        'interaction_trend_L90D_vs_avg',
        'interaction_trend_L90D_vs_early',
        'interactions_L30D',
        'interactions_L30D_log',
        'interactions_L90D',
        'interactions_L90D_log',
        'posts_L30D',
        'posts_L90D',
        'received_comments_count',
        'received_comments_count_L30D',
        'received_comments_count_L30D_log',
        'received_comments_count_L90D',
        'received_comments_count_L90D_log',
        'received_comments_count_log',
        'total_comments_made',
        'total_comments_made_log',
        'total_interactions',
        'total_interactions_log',
        'total_posts',
        'total_posts_log'
    ],
    'H2_network_centrality': [
        'betweenness_centrality',
        'in_degree_centrality',
        'in_degree_resolved_mention',
        'in_degree_resolved_mention_log',
        'in_degree_weighted_mention',
        'in_degree_weighted_mention_log',
        'out_degree_centrality',
        'pagerank'
    ],
    'H3_feedback_existence': [
        'avg_received_sentiment',
        'avg_received_sentiment_L30D',
        'avg_received_sentiment_L90D',
        'negative_feedback_ratio',
        'negative_feedback_ratio_L30D',
        'negative_feedback_ratio_L90D',
        'neutral_feedback_ratio',
        'positive_feedback_ratio',
        'positive_feedback_ratio_L30D',
        'positive_feedback_ratio_L90D',
        'strong_negative_feedback_ratio',
        'strong_positive_feedback_ratio'
    ],
    'H4_early_activity': [
        'active_months',
        'community_age_months',
        'early_activity',
        'early_activity_log'
    ]
}

# 四阈值显著性结果汇总
SIGNIFICANCE_RESULTS = {
    '90天': {
        'H1_social_interaction': {'total': 31, 'significant': 18, 'rate': 0.581},
        'H2_network_centrality': {'total': 8, 'significant': 6, 'rate': 0.750},
        'H3_feedback_existence': {'total': 12, 'significant': 8, 'rate': 0.667},
        'H4_early_activity': {'total': 4, 'significant': 3, 'rate': 0.750}
    },
    '150天': {
        'H1_social_interaction': {'total': 31, 'significant': 17, 'rate': 0.548},
        'H2_network_centrality': {'total': 8, 'significant': 6, 'rate': 0.750},
        'H3_feedback_existence': {'total': 12, 'significant': 7, 'rate': 0.583},
        'H4_early_activity': {'total': 4, 'significant': 2, 'rate': 0.500}
    },
    '180天': {
        'H1_social_interaction': {'total': 31, 'significant': 13, 'rate': 0.419},
        'H2_network_centrality': {'total': 8, 'significant': 6, 'rate': 0.750},
        'H3_feedback_existence': {'total': 12, 'significant': 6, 'rate': 0.500},
        'H4_early_activity': {'total': 4, 'significant': 2, 'rate': 0.500}
    },
    '330天': {
        'H1_social_interaction': {'total': 31, 'significant': 16, 'rate': 0.516},
        'H2_network_centrality': {'total': 8, 'significant': 6, 'rate': 0.750},
        'H3_feedback_existence': {'total': 12, 'significant': 5, 'rate': 0.417},
        'H4_early_activity': {'total': 4, 'significant': 3, 'rate': 0.750}
    }
}

def create_comprehensive_summary():
    """创建综合汇总表"""
    
    print("=" * 100)
    print("📊 论文理论变量 vs 实验实际变量 vs 显著性结果 - 综合汇总分析")
    print("=" * 100)
    
    for hyp_id, hyp_info in PAPER_THEORETICAL_VARIABLES.items():
        print(f"\n🎯 {hyp_info['name']}")
        print("-" * 80)
        print(f"理论描述: {hyp_info['description']}")
        
        # 论文理论变量
        print(f"\n📝 论文理论变量 ({len(hyp_info['theory_vars'])}个):")
        for var in hyp_info['theory_vars']:
            # 检查是否在实验中存在
            exists_in_exp = var in EXPERIMENT_ACTUAL_VARIABLES[hyp_id]
            status = "✅ 存在" if exists_in_exp else "❌ 缺失"
            print(f"   • {var:<30} {status}")
        
        # 实验实际变量
        print(f"\n🔬 实验实际变量 ({len(EXPERIMENT_ACTUAL_VARIABLES[hyp_id])}个):")
        for var in EXPERIMENT_ACTUAL_VARIABLES[hyp_id]:
            # 检查是否为论文理论变量
            is_theory = var in hyp_info['theory_vars']
            var_type = "📝 理论" if is_theory else "➕ 扩展"
            print(f"   • {var:<35} {var_type}")
        
        # 显著性结果
        print(f"\n📈 四阈值显著性结果:")
        print(f"{'阈值':<8} {'总变量':<8} {'显著数':<8} {'显著率':<10} {'评价'}")
        print("-" * 50)
        
        for threshold in ['90天', '150天', '180天', '330天']:
            result = SIGNIFICANCE_RESULTS[threshold][hyp_id]
            rate_str = f"{result['rate']:.1%}"
            
            # 评价显著性水平
            if result['rate'] >= 0.7:
                evaluation = "🏆 优秀"
            elif result['rate'] >= 0.6:
                evaluation = "✅ 良好"
            elif result['rate'] >= 0.5:
                evaluation = "⚠️ 中等"
            else:
                evaluation = "❌ 较弱"
            
            print(f"{threshold:<8} {result['total']:<8} {result['significant']:<8} {rate_str:<10} {evaluation}")
        
        # 计算平均显著率
        avg_rate = np.mean([SIGNIFICANCE_RESULTS[t][hyp_id]['rate'] for t in ['90天', '150天', '180天', '330天']])
        print(f"\n📊 平均显著率: {avg_rate:.1%}")
        
        print("\n" + "=" * 80)

def create_variable_matching_analysis():
    """创建变量匹配度分析"""
    
    print("\n" + "=" * 100)
    print("🔍 论文理论变量在实验中的匹配度分析")
    print("=" * 100)
    
    total_theory_vars = 0
    matched_theory_vars = 0
    
    for hyp_id, hyp_info in PAPER_THEORETICAL_VARIABLES.items():
        theory_vars = hyp_info['theory_vars']
        actual_vars = EXPERIMENT_ACTUAL_VARIABLES[hyp_id]
        
        matched = [var for var in theory_vars if var in actual_vars]
        missing = [var for var in theory_vars if var not in actual_vars]
        
        total_theory_vars += len(theory_vars)
        matched_theory_vars += len(matched)
        
        print(f"\n{hyp_info['name']}:")
        print(f"   理论变量总数: {len(theory_vars)}")
        print(f"   匹配变量数: {len(matched)}")
        print(f"   匹配率: {len(matched)/len(theory_vars):.1%}")
        
        if matched:
            print(f"   ✅ 匹配变量: {', '.join(matched)}")
        if missing:
            print(f"   ❌ 缺失变量: {', '.join(missing)}")
    
    print(f"\n📊 总体匹配度: {matched_theory_vars}/{total_theory_vars} ({matched_theory_vars/total_theory_vars:.1%})")

def create_significance_ranking():
    """创建显著性排名分析"""
    
    print("\n" + "=" * 100)
    print("🏆 假设显著性表现排名（基于平均显著率）")
    print("=" * 100)
    
    # 计算每个假设的平均显著率
    hypothesis_performance = []
    
    for hyp_id, hyp_info in PAPER_THEORETICAL_VARIABLES.items():
        rates = [SIGNIFICANCE_RESULTS[t][hyp_id]['rate'] for t in ['90天', '150天', '180天', '330天']]
        avg_rate = np.mean(rates)
        std_rate = np.std(rates)
        
        hypothesis_performance.append({
            'hypothesis': hyp_info['name'],
            'avg_rate': avg_rate,
            'std_rate': std_rate,
            'stability': 'high' if std_rate < 0.1 else 'medium' if std_rate < 0.2 else 'low'
        })
    
    # 按平均显著率排序
    hypothesis_performance.sort(key=lambda x: x['avg_rate'], reverse=True)
    
    print(f"{'排名':<4} {'假设':<20} {'平均显著率':<12} {'标准差':<10} {'稳定性':<8} {'评价'}")
    print("-" * 70)
    
    for i, perf in enumerate(hypothesis_performance, 1):
        stability_icon = "🔒" if perf['stability'] == 'high' else "⚖️" if perf['stability'] == 'medium' else "📈"
        
        if perf['avg_rate'] >= 0.7:
            evaluation = "🏆 卓越"
        elif perf['avg_rate'] >= 0.6:
            evaluation = "✅ 优秀"
        elif perf['avg_rate'] >= 0.5:
            evaluation = "⚠️ 良好"
        else:
            evaluation = "❌ 需改进"
        
        rate_str = f"{perf['avg_rate']:.1%}"
        std_str = f"{perf['std_rate']:.3f}"
        print(f"{i:<4} {perf['hypothesis']:<20} {rate_str:<12} {std_str:<10} {stability_icon}{perf['stability']:<7} {evaluation}")

if __name__ == "__main__":
    create_comprehensive_summary()
    create_variable_matching_analysis()
    create_significance_ranking()
    
    print("\n" + "=" * 100)
    print("📋 总结与建议")
    print("=" * 100)
    print("1. 🎯 H2假设表现最佳：75.0%平均显著率，跨时间稳定性最高")
    print("2. 📊 H1假设变量最丰富：31个变量，但显著率中等(51.6%)")
    print("3. 💬 H3假设时间敏感：显著率从66.7%降至41.7%")
    print("4. 🚀 H4假设呈U型：90天和330天表现好(75.0%)，中期较弱(50.0%)")
    print("5. ✅ 理论变量匹配度高：核心变量基本都存在于实验数据中")
    print("=" * 100)
