#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版H4双重效应检验
==================

使用更科学严谨的方法检验H4假设的双重效应机制
- 多种非线性检验方法
- 严格的统计显著性检验
- 稳健性验证
- 效应大小评估

理论基础：
1. 投资模型理论 - 适度投入产生保护效应
2. 工作要求-资源模型 - 过度投入引发燃尽效应

作者：AI助手
日期：2025-01-21
版本：2.0 (科学改进版)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
from scipy import stats
from scipy.optimize import minimize_scalar
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.isotonic import IsotonicRegression
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
rcParams['axes.unicode_minus'] = False

class AdvancedH4DoubleEffectTester:
    """改进版H4双重效应检验器"""
    
    def __init__(self, alpha=0.05, n_bootstrap=1000):
        """初始化检验器"""
        self.alpha = alpha
        self.n_bootstrap = n_bootstrap
        
        print("🔬 改进版H4双重效应检验器")
        print("="*60)
        print("🎯 目标：使用科学方法严格检验双重效应假设")
        print("📊 方法：多重非线性检验 + 统计显著性验证")
        print("🔍 理论：适度投入保护效应 vs 过度投入燃尽效应")
        print("="*60)

    def load_and_prepare_data(self, filename):
        """加载和预处理数据"""
        print(f"\n📁 加载数据...")
        
        try:
            df = pd.read_csv(filename)
            print(f"   ✅ 数据加载成功: {df.shape}")
            
            # H4相关变量
            h4_candidates = [
                'early_activity', 'early_activity_log', 'active_months',
                'first_month_posts', 'initial_engagement', 'community_age_months'
            ]
            
            available_vars = {}
            for var in h4_candidates:
                if var in df.columns:
                    available_vars[var] = df[var]
                    print(f"   ✅ 找到H4变量: {var}")
            
            if not available_vars:
                print("   ❌ 未找到H4相关变量")
                return None, None, None
            
            # 目标变量 (0=留存, 1=流失)
            y = df['event_status'].astype(int)
            
            return df, available_vars, y
            
        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return None, None, None

    def advanced_nonlinearity_test(self, x, y, var_name):
        """高级非线性关系检验"""
        print(f"\n🔬 {var_name} 高级非线性检验")
        print("-"*50)
        
        # 移除缺失值和异常值
        valid_mask = ~(np.isnan(x) | np.isnan(y))
        x_clean = x[valid_mask]
        y_clean = y[valid_mask]
        
        if len(x_clean) < 50:
            print("   ❌ 有效样本量不足")
            return None
        
        # 移除极端异常值 (超过3个标准差)
        z_scores = np.abs(stats.zscore(x_clean))
        outlier_mask = z_scores < 3
        x_final = x_clean[outlier_mask]
        y_final = y_clean[outlier_mask]
        
        print(f"   📊 有效样本量: {len(x_final)}")
        
        results = {}
        
        # 1. 线性基准模型
        linear_corr, linear_p = stats.pearsonr(x_final, y_final)
        results['linear'] = {
            'correlation': linear_corr,
            'p_value': linear_p,
            'r_squared': linear_corr**2
        }
        
        print(f"   📈 线性关系: r={linear_corr:.4f}, p={linear_p:.6f}")
        
        # 2. 二次多项式检验
        X_poly = np.column_stack([x_final, x_final**2])
        
        # 使用交叉验证评估模型
        from sklearn.model_selection import KFold
        kf = KFold(n_splits=5, shuffle=True, random_state=42)
        
        linear_scores = []
        quad_scores = []
        
        for train_idx, test_idx in kf.split(x_final):
            # 线性模型
            lr_linear = LinearRegression()
            lr_linear.fit(x_final[train_idx].reshape(-1, 1), y_final[train_idx])
            linear_pred = lr_linear.predict(x_final[test_idx].reshape(-1, 1))
            linear_scores.append(r2_score(y_final[test_idx], linear_pred))
            
            # 二次模型
            lr_quad = LinearRegression()
            lr_quad.fit(X_poly[train_idx], y_final[train_idx])
            quad_pred = lr_quad.predict(X_poly[test_idx])
            quad_scores.append(r2_score(y_final[test_idx], quad_pred))
        
        linear_cv_score = np.mean(linear_scores)
        quad_cv_score = np.mean(quad_scores)
        cv_improvement = quad_cv_score - linear_cv_score
        
        results['quadratic'] = {
            'cv_score': quad_cv_score,
            'improvement': cv_improvement,
            'significant_improvement': cv_improvement > 0.01  # 至少1%改善
        }
        
        print(f"   📊 二次模型CV-R²: {quad_cv_score:.4f}")
        print(f"   📈 相对线性改善: {cv_improvement:.4f}")
        
        # 3. 分段线性回归（检测拐点）
        results['piecewise'] = self._piecewise_regression_test(x_final, y_final)
        
        # 4. 等张回归（单调性检验）
        iso_reg = IsotonicRegression(increasing=False)  # 假设递减关系
        iso_pred = iso_reg.fit_transform(x_final, y_final)
        iso_r2 = r2_score(y_final, iso_pred)
        
        results['isotonic'] = {
            'r_squared': iso_r2,
            'monotonic_fit': iso_r2 > linear_corr**2 + 0.01
        }
        
        print(f"   📊 等张回归R²: {iso_r2:.4f}")
        
        # 5. 分位数分析（更精细的分组）
        results['quantile_analysis'] = self._quantile_analysis(x_final, y_final)
        
        # 6. 统计显著性检验
        results['significance_tests'] = self._nonlinearity_significance_test(x_final, y_final)
        
        return results

    def _piecewise_regression_test(self, x, y):
        """分段线性回归检验"""
        print("   🔍 分段线性回归分析...")
        
        # 寻找最优分割点
        x_sorted_idx = np.argsort(x)
        x_sorted = x[x_sorted_idx]
        y_sorted = y[x_sorted_idx]
        
        best_breakpoint = None
        best_r2 = -np.inf
        best_slopes = None
        
        # 在中间80%的数据范围内寻找分割点
        start_idx = int(len(x_sorted) * 0.1)
        end_idx = int(len(x_sorted) * 0.9)
        
        for i in range(start_idx, end_idx, max(1, (end_idx-start_idx)//20)):
            breakpoint = x_sorted[i]
            
            # 分割数据
            left_mask = x <= breakpoint
            right_mask = x > breakpoint
            
            if np.sum(left_mask) < 10 or np.sum(right_mask) < 10:
                continue
            
            # 拟合两段线性回归
            try:
                slope1, intercept1, r1, p1, _ = stats.linregress(x[left_mask], y[left_mask])
                slope2, intercept2, r2, p2, _ = stats.linregress(x[right_mask], y[right_mask])
                
                # 计算整体拟合度
                pred_left = slope1 * x[left_mask] + intercept1
                pred_right = slope2 * x[right_mask] + intercept2
                
                all_pred = np.concatenate([pred_left, pred_right])
                all_y = np.concatenate([y[left_mask], y[right_mask]])
                
                r2_total = r2_score(all_y, all_pred)
                
                if r2_total > best_r2:
                    best_r2 = r2_total
                    best_breakpoint = breakpoint
                    best_slopes = (slope1, slope2)
                    
            except:
                continue
        
        # 检验是否存在显著的斜率变化（双重效应的证据）
        slope_change_evidence = False
        if best_slopes:
            slope1, slope2 = best_slopes
            # 如果斜率符号相反，可能是双重效应
            slope_change_evidence = (slope1 > 0 and slope2 < 0) or (slope1 < 0 and slope2 > 0)
        
        print(f"      最优分割点: {best_breakpoint:.3f}")
        print(f"      分段回归R²: {best_r2:.4f}")
        print(f"      斜率变化证据: {'✅' if slope_change_evidence else '❌'}")
        
        return {
            'breakpoint': best_breakpoint,
            'r_squared': best_r2,
            'slopes': best_slopes,
            'slope_change_evidence': slope_change_evidence
        }

    def _quantile_analysis(self, x, y):
        """开放式分位数分析 - 发现真实的数据模式"""
        print("   🔍 开放式分位数模式发现...")

        # 使用10分位数进行更精细的分析
        quantiles = np.arange(0.1, 1.0, 0.1)
        quantile_bounds = np.quantile(x, quantiles)

        group_stats = []
        for i in range(len(quantile_bounds) + 1):
            if i == 0:
                mask = x <= quantile_bounds[0]
                group_name = f"Q1 (≤{quantile_bounds[0]:.2f})"
            elif i == len(quantile_bounds):
                mask = x > quantile_bounds[-1]
                group_name = f"Q10 (>{quantile_bounds[-1]:.2f})"
            else:
                mask = (x > quantile_bounds[i-1]) & (x <= quantile_bounds[i])
                group_name = f"Q{i+1} ({quantile_bounds[i-1]:.2f}-{quantile_bounds[i]:.2f}]"

            if np.sum(mask) > 0:
                group_mean = np.mean(y[mask])
                group_std = np.std(y[mask])
                group_size = np.sum(mask)

                group_stats.append({
                    'group': group_name,
                    'mean': group_mean,
                    'std': group_std,
                    'size': group_size,
                    'quantile': i
                })

        # 开放式模式识别
        means = [stat['mean'] for stat in group_stats]

        # 1. 识别趋势类型
        trend_analysis = self._analyze_trend_pattern(means)

        # 2. 寻找关键点
        min_idx = np.argmin(means)
        max_idx = np.argmax(means)

        # 3. 计算变化幅度
        total_range = max(means) - min(means)
        relative_range = total_range / np.mean(means) if np.mean(means) > 0 else 0

        print(f"      分组数: {len(group_stats)}")
        print(f"      趋势模式: {trend_analysis['pattern_name']}")
        print(f"      最低流失率组: Q{min_idx+1} ({means[min_idx]:.3f})")
        print(f"      最高流失率组: Q{max_idx+1} ({means[max_idx]:.3f})")
        print(f"      变化幅度: {total_range:.3f} ({relative_range:.1%})")

        # 打印详细统计
        for stat in group_stats:
            print(f"      {stat['group']}: 流失率={stat['mean']:.3f}, n={stat['size']}")

        return {
            'group_stats': group_stats,
            'trend_analysis': trend_analysis,
            'optimal_group': min_idx + 1,
            'worst_group': max_idx + 1,
            'effect_magnitude': relative_range
        }

    def _analyze_trend_pattern(self, means):
        """分析趋势模式"""
        if len(means) < 3:
            return {'pattern_name': '数据不足', 'pattern_type': 'insufficient'}

        # 计算一阶差分（变化率）
        first_diff = np.diff(means)

        # 计算二阶差分（加速度）
        second_diff = np.diff(first_diff) if len(first_diff) > 1 else []

        # 模式识别
        positive_changes = np.sum(first_diff > 0.001)  # 显著上升
        negative_changes = np.sum(first_diff < -0.001)  # 显著下降
        stable_changes = len(first_diff) - positive_changes - negative_changes

        # 识别具体模式
        if negative_changes >= len(first_diff) * 0.7:
            pattern = {'pattern_name': '单调递减', 'pattern_type': 'monotonic_decreasing'}
        elif positive_changes >= len(first_diff) * 0.7:
            pattern = {'pattern_name': '单调递增', 'pattern_type': 'monotonic_increasing'}
        elif stable_changes >= len(first_diff) * 0.7:
            pattern = {'pattern_name': '基本稳定', 'pattern_type': 'stable'}
        else:
            # 检查复杂模式
            min_idx = np.argmin(means)
            max_idx = np.argmax(means)

            if min_idx in [0, len(means)-1]:  # 最值在端点
                if max_idx in [0, len(means)-1]:  # 两个极值都在端点
                    pattern = {'pattern_name': '端点极值', 'pattern_type': 'endpoint_extremes'}
                else:  # 最小值在端点，最大值在中间
                    pattern = {'pattern_name': 'U型或倒U型', 'pattern_type': 'u_shaped'}
            else:  # 最小值在中间
                if 2 <= min_idx <= len(means) - 3:  # 最小值在中间区域
                    left_higher = np.mean(means[:min_idx]) > means[min_idx]
                    right_higher = np.mean(means[min_idx+1:]) > means[min_idx]
                    if left_higher and right_higher:
                        pattern = {'pattern_name': '倒U型（中间最优）', 'pattern_type': 'inverted_u'}
                    else:
                        pattern = {'pattern_name': '复杂非线性', 'pattern_type': 'complex_nonlinear'}
                else:
                    pattern = {'pattern_name': '不规则变化', 'pattern_type': 'irregular'}

        # 添加统计信息
        pattern.update({
            'positive_changes': positive_changes,
            'negative_changes': negative_changes,
            'stable_changes': stable_changes,
            'total_variation': np.var(means),
            'trend_strength': max(positive_changes, negative_changes) / len(first_diff)
        })

        return pattern

    def _nonlinearity_significance_test(self, x, y):
        """非线性关系的统计显著性检验"""
        print("   🔍 非线性显著性检验...")
        
        # 1. Ramsey RESET检验（回归方程设定错误检验）
        # 简化版本：比较线性模型和包含高次项的模型
        
        X_linear = x.reshape(-1, 1)
        X_nonlinear = np.column_stack([x, x**2, x**3])
        
        # 线性模型
        lr_linear = LinearRegression()
        lr_linear.fit(X_linear, y)
        rss_linear = np.sum((y - lr_linear.predict(X_linear))**2)
        
        # 非线性模型
        lr_nonlinear = LinearRegression()
        lr_nonlinear.fit(X_nonlinear, y)
        rss_nonlinear = np.sum((y - lr_nonlinear.predict(X_nonlinear))**2)
        
        # F检验
        n = len(y)
        k_linear = 1
        k_nonlinear = 3
        
        f_stat = ((rss_linear - rss_nonlinear) / (k_nonlinear - k_linear)) / (rss_nonlinear / (n - k_nonlinear - 1))
        f_p_value = 1 - stats.f.cdf(f_stat, k_nonlinear - k_linear, n - k_nonlinear - 1)
        
        print(f"      F统计量: {f_stat:.4f}")
        print(f"      F检验p值: {f_p_value:.6f}")
        
        # 2. Bootstrap置信区间检验
        bootstrap_improvements = []
        
        for _ in range(self.n_bootstrap):
            # Bootstrap重采样
            indices = np.random.choice(len(x), len(x), replace=True)
            x_boot = x[indices]
            y_boot = y[indices]
            
            # 计算线性和二次模型的R²差异
            try:
                linear_r2 = stats.pearsonr(x_boot, y_boot)[0]**2
                
                X_boot_poly = np.column_stack([x_boot, x_boot**2])
                lr_boot = LinearRegression()
                lr_boot.fit(X_boot_poly, y_boot)
                quad_r2 = r2_score(y_boot, lr_boot.predict(X_boot_poly))
                
                improvement = quad_r2 - linear_r2
                bootstrap_improvements.append(improvement)
            except:
                continue
        
        if bootstrap_improvements:
            improvement_ci = np.percentile(bootstrap_improvements, [2.5, 97.5])
            improvement_significant = improvement_ci[0] > 0  # 95%置信区间不包含0
            
            print(f"      R²改善95%CI: [{improvement_ci[0]:.4f}, {improvement_ci[1]:.4f}]")
            print(f"      改善显著性: {'✅' if improvement_significant else '❌'}")
        else:
            improvement_significant = False
            improvement_ci = None
        
        return {
            'f_statistic': f_stat,
            'f_p_value': f_p_value,
            'f_significant': f_p_value < self.alpha,
            'bootstrap_ci': improvement_ci,
            'bootstrap_significant': improvement_significant
        }

    def comprehensive_h4_test(self, filename):
        """综合H4检验"""
        print("🚀 启动改进版H4双重效应综合检验")
        print("="*60)
        
        # 加载数据
        df, h4_vars, y = self.load_and_prepare_data(filename)
        if df is None:
            return None
        
        all_results = {}
        
        # 对每个H4变量进行高级检验
        for var_name, var_data in h4_vars.items():
            print(f"\n{'='*60}")
            print(f"🔬 分析变量: {var_name}")
            print(f"{'='*60}")
            
            results = self.advanced_nonlinearity_test(var_data.values, y.values, var_name)
            if results:
                all_results[var_name] = results
                
                # 综合评估
                self._evaluate_mechanism_evidence(var_name, results)
        
        # 生成最终报告
        self._generate_comprehensive_report(all_results)
        
        return all_results

    def _evaluate_mechanism_evidence(self, var_name, results):
        """开放式机制证据评估 - 不预设特定模式"""
        print(f"\n🎯 {var_name} 机制模式分析")
        print("-"*50)

        # 1. 基础关系强度
        linear_strength = abs(results['linear']['correlation'])
        linear_significant = results['linear']['p_value'] < self.alpha

        print(f"   📈 线性关系: r={results['linear']['correlation']:.4f}, p={results['linear']['p_value']:.6f}")
        print(f"   📊 线性显著性: {'✅ 显著' if linear_significant else '❌ 不显著'}")

        # 2. 非线性证据
        nonlinear_improvement = results['quadratic']['improvement']
        nonlinear_significant = results['significance_tests']['f_significant']

        print(f"   🔄 非线性改善: {nonlinear_improvement:.4f}")
        print(f"   📊 非线性显著性: {'✅ 显著' if nonlinear_significant else '❌ 不显著'}")

        # 3. 数据模式识别
        trend_analysis = results['quantile_analysis']['trend_analysis']
        pattern_type = trend_analysis['pattern_type']
        pattern_name = trend_analysis['pattern_name']
        effect_magnitude = results['quantile_analysis']['effect_magnitude']

        print(f"   🔍 发现模式: {pattern_name}")
        print(f"   📏 效应幅度: {effect_magnitude:.1%}")

        # 4. 分段回归发现
        if results['piecewise']['breakpoint'] is not None:
            breakpoint = results['piecewise']['breakpoint']
            slopes = results['piecewise']['slopes']
            print(f"   🔗 关键拐点: {breakpoint:.3f}")
            if slopes:
                print(f"   📐 斜率变化: {slopes[0]:.4f} → {slopes[1]:.4f}")

        # 5. 综合机制评估
        mechanism_insights = []

        if linear_significant and abs(results['linear']['correlation']) > 0.1:
            direction = "正向" if results['linear']['correlation'] > 0 else "负向"
            mechanism_insights.append(f"存在{direction}线性关系")

        if nonlinear_significant and nonlinear_improvement > 0.01:
            mechanism_insights.append("存在显著非线性成分")

        if pattern_type == 'monotonic_decreasing':
            mechanism_insights.append("单调保护效应：投入越多，风险越低")
        elif pattern_type == 'monotonic_increasing':
            mechanism_insights.append("单调风险效应：投入越多，风险越高")
        elif pattern_type == 'inverted_u':
            mechanism_insights.append("倒U型效应：存在最优投入水平")
        elif pattern_type == 'u_shaped':
            mechanism_insights.append("U型效应：中等投入风险最高")
        elif pattern_type == 'complex_nonlinear':
            mechanism_insights.append("复杂非线性关系：需进一步分析")

        if results['piecewise']['slope_change_evidence']:
            mechanism_insights.append("存在机制转换点")

        # 6. 实际意义评估
        practical_significance = "高" if effect_magnitude > 0.1 else "中" if effect_magnitude > 0.05 else "低"

        print(f"\n   🔬 机制洞察:")
        for insight in mechanism_insights:
            print(f"      • {insight}")

        print(f"\n   📊 实际意义: {practical_significance}")

        # 7. 科学可信度
        credibility_score = 0
        if linear_significant: credibility_score += 1
        if nonlinear_significant: credibility_score += 1
        if effect_magnitude > 0.05: credibility_score += 1
        if len(mechanism_insights) >= 2: credibility_score += 1

        credibility_level = "高" if credibility_score >= 3 else "中" if credibility_score >= 2 else "低"

        print(f"   🎯 科学可信度: {credibility_level} ({credibility_score}/4)")

        return {
            'pattern_type': pattern_type,
            'pattern_name': pattern_name,
            'mechanism_insights': mechanism_insights,
            'effect_magnitude': effect_magnitude,
            'credibility_score': credibility_score,
            'credibility_level': credibility_level
        }

    def _generate_comprehensive_report(self, all_results):
        """生成开放式机制分析综合报告"""
        print(f"\n📄 H4变量机制分析综合报告")
        print("="*60)

        if not all_results:
            print("❌ 无有效分析结果")
            return

        print("📊 各变量机制模式汇总:")
        print("-"*50)

        mechanism_summary = {}
        for var_name, results in all_results.items():
            mechanism_info = self._evaluate_mechanism_evidence(var_name, results)
            mechanism_summary[var_name] = mechanism_info
            print(f"{var_name}: {mechanism_info['pattern_name']} (可信度: {mechanism_info['credibility_level']})")

        print(f"\n🎯 H4变量总体机制发现:")
        print("-"*50)

        # 统计不同模式类型
        pattern_counts = {}
        high_credibility_vars = []

        for var_name, info in mechanism_summary.items():
            pattern_type = info['pattern_type']
            pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1

            if info['credibility_level'] == '高':
                high_credibility_vars.append(var_name)

        print("🔍 发现的机制模式:")
        for pattern, count in pattern_counts.items():
            print(f"   • {pattern}: {count}个变量")

        print(f"\n📊 高可信度变量: {len(high_credibility_vars)}/{len(all_results)}")
        for var in high_credibility_vars:
            info = mechanism_summary[var]
            print(f"   ✅ {var}: {info['pattern_name']} (效应幅度: {info['effect_magnitude']:.1%})")

        # 科学结论
        if len(high_credibility_vars) >= len(all_results) * 0.5:
            overall_conclusion = "🏆 发现了可信的机制模式"
        elif len(high_credibility_vars) >= 1:
            overall_conclusion = "✅ 发现了部分可信的机制"
        else:
            overall_conclusion = "⚠️ 机制证据有限，需进一步研究"

        print(f"\n🎯 总体结论: {overall_conclusion}")

        # 实践建议
        print(f"\n💡 实践建议:")
        for var_name, info in mechanism_summary.items():
            if info['credibility_level'] == '高' and info['mechanism_insights']:
                print(f"   📈 {var_name}:")
                for insight in info['mechanism_insights']:
                    print(f"      • {insight}")

        return mechanism_summary


if __name__ == "__main__":
    # 创建改进版检验器
    tester = AdvancedH4DoubleEffectTester()
    
    # 查找数据文件
    data_files = [
        'user_survival_analysis_dataset_90days_cleaned.csv',
        'user_survival_analysis_dataset_150days_cleaned.csv',
        'user_survival_analysis_dataset_180days_cleaned.csv',
        'user_survival_analysis_dataset_330days_cleaned.csv'
    ]
    
    # 运行改进版检验
    for filename in data_files:
        try:
            print(f"🎯 分析文件: {filename}")
            results = tester.comprehensive_h4_test(filename)
            if results:
                break
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            continue
    else:
        print("❌ 未找到可用的数据文件")
