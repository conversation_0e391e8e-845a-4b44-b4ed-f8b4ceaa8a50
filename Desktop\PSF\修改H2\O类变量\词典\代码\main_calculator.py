#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
O变量计算主程序
整合所有模块，计算O变量并写入四阈值数据集

作者: AI助手
日期: 2025-01-11
版本: 1.0
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from dict_loader import DictionaryLoader
from o_variable_scorer import OVariableScorer
from data_processor import DataProcessor

class OVariableCalculator:
    """O变量计算器主程序"""
    
    def __init__(self, project_root: str = "../../.."):
        """
        初始化O变量计算器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = project_root
        self.dictionary_loader = None
        self.scorer = None
        self.data_processor = None
        self.results = {}
        
        print("🚀 O变量计算器初始化 - 整合您的建议")
        print("🎯 改进：PCA权重 + 科学置信度 + 中性值控制")
        print("="*60)
    
    def initialize_components(self) -> bool:
        """初始化所有组件"""
        print("🔧 初始化组件...")
        
        try:
            # 1. 初始化词典加载器 (使用当前词典目录)
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 回到词典目录
            self.dictionary_loader = DictionaryLoader(current_dir)
            
            # 2. 加载词典
            dalian_success = self.dictionary_loader.load_dalian_emotions()
            liwc_success = self.dictionary_loader.load_liwc_dictionary()
            
            if not (dalian_success and liwc_success):
                print("❌ 词典加载失败")
                return False
            
            # 3. 保存处理后的词典
            dict_success = self.dictionary_loader.save_processed_dictionaries()
            if not dict_success:
                print("❌ 词典处理失败")
                return False
            
            # 4. 初始化评分器
            dict_file_path = os.path.join(current_dir, "processed_dictionaries.json")
            self.scorer = OVariableScorer(dict_file_path)
            scorer_success = self.scorer.load_dictionaries()
            if not scorer_success:
                print("❌ 评分器初始化失败")
                return False
            
            # 5. 初始化数据处理器 (修正路径)
            # 直接使用正确的项目根目录
            actual_project_root = r"C:\Users\<USER>\Desktop\PSF\修改H2"
            print(f"   项目根目录: {actual_project_root}")
            self.data_processor = DataProcessor(actual_project_root)
            
            # 6. 加载数据
            comment_success = self.data_processor.load_comment_data()
            threshold_success = self.data_processor.load_threshold_datasets()
            
            if not (comment_success and threshold_success):
                print("❌ 数据加载失败")
                return False
            
            print("✅ 所有组件初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 组件初始化失败: {e}")
            return False
    
    def calculate_o_variables_for_threshold(self, threshold: int) -> bool:
        """
        为指定阈值计算O变量
        
        Args:
            threshold: 时间阈值
            
        Returns:
            bool: 计算是否成功
        """
        print(f"\n🎯 计算 {threshold}天阈值的O变量")
        print("-" * 40)
        
        try:
            # 1. 匹配用户与评论
            users_with_comments, users_without_comments, user_id_col = \
                self.data_processor.match_users_with_comments(threshold)
            
            # 2. 获取原始数据集
            original_df = self.data_processor.threshold_datasets[threshold].copy()
            
            print(f"🔄 开始计算O变量...")
            print(f"   有评论用户: {len(users_with_comments)} 个")
            print(f"   无评论用户: {len(users_without_comments)} 个")
            
            # 3. 计算有评论用户的O变量
            o_variable_results = []

            # 先收集样本文本用于PCA权重计算
            sample_texts = []
            for user_id in users_with_comments[:min(500, len(users_with_comments))]:  # 最多500个样本
                comment_text = self.data_processor.get_user_comment_text(user_id)
                if comment_text.strip():
                    sample_texts.append(comment_text)

            # 计算PCA权重
            if len(sample_texts) >= 100:
                print(f"🔬 基于 {len(sample_texts)} 个样本计算PCA权重...")
                self.scorer.calculate_pca_weights(sample_texts)
            else:
                print(f"⚠️ 样本不足 ({len(sample_texts)})，使用默认权重")

            # 处理有评论的用户
            for i, user_id in enumerate(users_with_comments):
                if (i + 1) % 100 == 0:
                    print(f"   已处理有评论用户: {i + 1}/{len(users_with_comments)}")

                comment_text = self.data_processor.get_user_comment_text(user_id)
                result = self.scorer.evaluate_user_text(comment_text, user_id)

                # 添加关键控制变量 - 按照您的建议
                result['has_comments_dummy'] = 1  # 有评论用户=1，无评论用户=0

                o_variable_results.append(result)
            
            # 处理无评论的用户 - 中性值填充
            for i, user_id in enumerate(users_without_comments):
                if (i + 1) % 500 == 0:
                    print(f"   已处理无评论用户: {i + 1}/{len(users_without_comments)}")

                # 中性值填充 - 按照您的建议：后续SOR模型必须用has_comments_dummy控制
                result = {
                    'user_id': user_id,
                    'text_length': 0,
                    'word_count': 0,
                    'Social_Efficacy_score': 50.0,  # 中性值
                    'Social_Efficacy_confidence': 0.0,
                    'social_matches': 0,
                    'Emotional_Stability_score': 50.0,  # 中性值
                    'Emotional_Stability_confidence': 0.0,
                    'emotion_matches': 0,
                    'emotion_balance': 0.0,
                    'has_text': False,  # 标记无评论
                    'has_comments_dummy': 0,  # 关键控制变量：SOR模型必须包含此变量
                    'overall_confidence': 0.0,
                    'analysis_timestamp': pd.Timestamp.now().isoformat(),
                    'imputation_method': 'neutral_default'
                }
                o_variable_results.append(result)
            
            # 4. 转换为DataFrame
            o_df = pd.DataFrame(o_variable_results)
            
            # 5. 合并到原始数据集
            # 确保用户ID类型一致
            original_df[user_id_col] = original_df[user_id_col].astype(str)
            o_df['user_id'] = o_df['user_id'].astype(str)
            
            # 合并数据
            enhanced_df = original_df.merge(
                o_df, 
                left_on=user_id_col, 
                right_on='user_id', 
                how='left'
            )
            
            # 删除重复的user_id列
            if 'user_id' in enhanced_df.columns and user_id_col != 'user_id':
                enhanced_df = enhanced_df.drop('user_id', axis=1)
            
            # 6. 保存增强后的数据集
            output_filename = f"SOR_enhanced_dataset_{threshold}days.csv"
            # 保存到项目根目录（动态路径）
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            output_path = os.path.join(project_root, output_filename)
            
            enhanced_df.to_csv(output_path, index=False, encoding='utf-8')
            
            print(f"💾 增强数据集已保存: {output_filename}")
            print(f"   原始数据: {original_df.shape}")
            print(f"   增强数据: {enhanced_df.shape}")
            print(f"   新增列数: {enhanced_df.shape[1] - original_df.shape[1]}")
            
            # 7. 保存统计信息
            self.results[threshold] = {
                'original_shape': original_df.shape,
                'enhanced_shape': enhanced_df.shape,
                'users_with_comments': len(users_with_comments),
                'users_without_comments': len(users_without_comments),
                'match_rate': len(users_with_comments) / len(original_df) * 100,
                'output_file': output_filename,
                'user_id_column': user_id_col,
                'processing_time': datetime.now().isoformat()
            }
            
            # 8. 显示O变量统计
            self._print_o_variable_statistics(enhanced_df, threshold)
            
            print(f"✅ {threshold}天阈值O变量计算完成")
            return True
            
        except Exception as e:
            print(f"❌ {threshold}天阈值O变量计算失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _print_single_variable_stats(self, df: pd.DataFrame, var_name: str, conf_name: str):
        """打印单个O变量的统计信息"""
        if var_name in df.columns:
            scores = df[var_name].dropna()
            print(f"   {var_name}:")
            print(f"     均值: {scores.mean():.2f}")
            print(f"     标准差: {scores.std():.2f}")
            print(f"     范围: [{scores.min():.2f}, {scores.max():.2f}]")

            if conf_name in df.columns:
                conf = df[conf_name].dropna()
                print(f"   {conf_name}:")
                print(f"     均值: {conf.mean():.3f}")
                print(f"     高置信度(>0.5): {(conf > 0.5).sum()} ({(conf > 0.5).mean()*100:.1f}%)")

    def _print_o_variable_statistics(self, df: pd.DataFrame, threshold: int):
        """打印O变量统计信息"""
        print(f"\n📊 {threshold}天阈值O变量统计:")

        # 使用辅助函数打印各变量统计
        self._print_single_variable_stats(df, 'Social_Efficacy_score', 'Social_Efficacy_confidence')
        self._print_single_variable_stats(df, 'Emotional_Stability_score', 'Emotional_Stability_confidence')

        # 有效文本统计
        if 'has_text' in df.columns:
            has_text_count = df['has_text'].sum()
            print(f"   有效文本用户: {has_text_count} ({has_text_count/len(df)*100:.1f}%)")
    
    def calculate_all_thresholds(self) -> bool:
        """计算所有阈值的O变量"""
        print("\n🎯 开始计算所有阈值的O变量")
        print("="*60)
        
        thresholds = [90, 150, 180, 330]
        success_count = 0
        
        for threshold in thresholds:
            if threshold in self.data_processor.threshold_datasets:
                success = self.calculate_o_variables_for_threshold(threshold)
                if success:
                    success_count += 1
            else:
                print(f"⚠️ {threshold}天数据集未加载，跳过")
        
        print(f"\n🎉 O变量计算完成: {success_count}/{len(thresholds)} 个阈值成功")
        
        # 保存总体结果
        self.save_calculation_report()
        
        return success_count > 0
    
    def save_calculation_report(self):
        """保存计算报告"""
        try:
            report = {
                'calculation_summary': {
                    'total_thresholds': len(self.results),
                    'successful_thresholds': list(self.results.keys()),
                    'calculation_time': datetime.now().isoformat()
                },
                'threshold_results': self.results,
                'component_info': {
                    'dictionary_loader': 'DictionaryLoader',
                    'scorer': 'OVariableScorer', 
                    'data_processor': 'DataProcessor'
                }
            }
            
            report_path = os.path.join(self.project_root, "O类变量", "O_variable_calculation_report.json")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"📋 计算报告已保存: O_variable_calculation_report.json")
            
        except Exception as e:
            print(f"❌ 计算报告保存失败: {e}")

def main():
    """主函数"""
    print("🚀 O变量计算主程序")
    print("="*60)
    print("📝 任务: 为四阈值数据集计算并添加O变量")
    print("🎯 目标: Social_Efficacy + Emotional_Stability")
    print("="*60)
    
    # 创建计算器
    calculator = OVariableCalculator()
    
    # 初始化组件
    if not calculator.initialize_components():
        print("❌ 组件初始化失败，程序退出")
        return
    
    # 计算所有阈值的O变量
    success = calculator.calculate_all_thresholds()
    
    if success:
        print("\n🎉 O变量计算任务完成!")
        print("📁 输出文件:")
        for threshold in calculator.results:
            print(f"   - SOR_enhanced_dataset_{threshold}days.csv")
        print("   - O_variable_calculation_report.json")
        print("\n✅ 所有四阈值数据集已成功增强O变量!")
    else:
        print("\n❌ O变量计算任务失败")

if __name__ == "__main__":
    main()
