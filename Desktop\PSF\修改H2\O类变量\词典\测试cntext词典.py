#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试cntext中文心理语言学词典功能
用于验证O变量分析的词典资源
"""

import cntext as ct
import pandas as pd
import json

def test_cntext_installation():
    """测试cntext安装和基本功能"""
    print("🔧 测试cntext安装...")
    print(f"cntext版本: {ct.__version__}")
    
    # 获取所有可用词典
    available_dicts = ct.dict_pkl_list()
    print(f"\n📚 可用词典数量: {len(available_dicts)}")
    print("可用词典列表:")
    for i, dict_name in enumerate(available_dicts, 1):
        print(f"  {i:2d}. {dict_name}")
    
    return available_dicts

def test_dutir_emotion_dict():
    """测试DUTIR情感词典"""
    print("\n" + "="*50)
    print("🎭 测试DUTIR情感词典（大连理工大学情感本体库）")
    print("="*50)
    
    # 加载DUTIR词典
    dutir_data = ct.load_pkl_dict('DUTIR.pkl')
    dutir_dict = dutir_data['DUTIR']
    
    print(f"词典描述: {dutir_data['Desc']}")
    print(f"参考文献: {dutir_data['Referer']}")
    
    print(f"\n情感类别: {list(dutir_dict.keys())}")
    
    # 显示每个情感类别的词汇数量和示例
    for emotion, words in dutir_dict.items():
        print(f"\n{emotion}类情感:")
        print(f"  词汇数量: {len(words)}")
        print(f"  示例词汇: {words[:10]}")  # 显示前10个词
    
    return dutir_dict

def test_sentiment_analysis():
    """测试情感分析功能"""
    print("\n" + "="*50)
    print("💭 测试情感分析功能")
    print("="*50)
    
    # 加载DUTIR词典
    dutir_dict = ct.load_pkl_dict('DUTIR.pkl')['DUTIR']
    
    # 测试文本
    test_texts = [
        "我今天心情很好，和朋友们一起学习，感觉很充实",
        "这个社区的氛围真不错，大家都很友善，我很喜欢这里",
        "我对这个平台很失望，感觉被忽视了，很难过",
        "学习新知识让我感到兴奋，我要继续努力",
        "我们团队合作得很好，每个人都很积极参与"
    ]
    
    results = []
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试文本 {i}: {text}")
        
        # 进行情感分析
        result = ct.sentiment(text=text, 
                             diction=dutir_dict, 
                             lang='chinese')
        
        print(f"分析结果: {result}")
        
        # 找出主要情感
        emotion_counts = {k: v for k, v in result.items() if k.endswith('_num') and k != 'stopword_num' and k != 'word_num' and k != 'sentence_num'}
        main_emotion = max(emotion_counts, key=emotion_counts.get) if emotion_counts else None
        
        if main_emotion and emotion_counts[main_emotion] > 0:
            emotion_name = main_emotion.replace('_num', '')
            print(f"主要情感: {emotion_name} (出现{emotion_counts[main_emotion]}次)")
        else:
            print("主要情感: 中性")
        
        results.append({
            'text': text,
            'result': result,
            'main_emotion': main_emotion
        })
    
    return results

def test_other_dicts():
    """测试其他重要词典"""
    print("\n" + "="*50)
    print("📖 测试其他重要词典")
    print("="*50)
    
    # 测试知网情感词典
    print("\n1. 知网HOWNET情感词典:")
    hownet_data = ct.load_pkl_dict('HOWNET.pkl')
    hownet_dict = hownet_data['HOWNET']
    print(f"   描述: {hownet_data['Desc']}")
    print(f"   类别: {list(hownet_dict.keys())}")
    for category, words in hownet_dict.items():
        print(f"   {category}: {len(words)}个词汇, 示例: {words[:5]}")
    
    # 测试中文情感银行
    print("\n2. 中文情感银行ChineseEmoBank:")
    emobank_data = ct.load_pkl_dict('ChineseEmoBank.pkl')
    emobank_dict = emobank_data['ChineseEmoBank']
    print(f"   描述: {emobank_data['Desc']}")
    print(f"   词汇数量: {len(emobank_dict)}")
    
    # 显示前5个词汇的效价和唤醒度
    sample_words = list(emobank_dict.items())[:5]
    print("   示例词汇(效价valence, 唤醒度arousal):")
    for word, values in sample_words:
        print(f"     {word}: 效价={values[0]:.2f}, 唤醒度={values[1]:.2f}")
    
    # 测试副词连词词典
    print("\n3. 副词连词词典ADV_CONJ:")
    adv_conj_data = ct.load_pkl_dict('ADV_CONJ.pkl')
    adv_conj_dict = adv_conj_data['ADV_CONJ']
    print(f"   描述: {adv_conj_data['Desc']}")
    print(f"   类别: {list(adv_conj_dict.keys())}")
    for category, words in adv_conj_dict.items():
        print(f"   {category}: {len(words)}个词汇, 示例: {words[:10]}")

def design_o_variables():
    """设计O变量的词典映射"""
    print("\n" + "="*50)
    print("🎯 设计O变量词典映射")
    print("="*50)
    
    # 加载相关词典
    dutir_dict = ct.load_pkl_dict('DUTIR.pkl')['DUTIR']
    hownet_dict = ct.load_pkl_dict('HOWNET.pkl')['HOWNET']
    adv_conj_dict = ct.load_pkl_dict('ADV_CONJ.pkl')['ADV_CONJ']
    
    # 设计O变量映射
    o_variable_mapping = {
        'social_efficacy': {
            'description': '社交效能感 - 用户在社交互动中的自信和成功感知',
            'positive_confidence': dutir_dict['好'][:20],  # 积极自信词汇
            'negative_doubt': dutir_dict['惧'][:20],       # 消极怀疑词汇
            'certainty_words': ['确定', '肯定', '相信', '确信', '明确'],
            'uncertainty_words': ['不确定', '怀疑', '犹豫', '迷茫', '困惑']
        },
        
        'community_belonging': {
            'description': '社区归属感 - 用户对社区的认同和归属感知',
            'collective_pronouns': ['我们', '大家', '一起', '共同', '团队', '集体'],
            'belonging_words': ['归属', '融入', '接纳', '认同', '家庭感'],
            'social_support': ['支持', '帮助', '关心', '理解', '鼓励', '陪伴'],
            'isolation_words': ['孤独', '排斥', '疏远', '隔离', '边缘']
        },
        
        'emotional_satisfaction': {
            'description': '情感满足度 - 用户的情感体验和满足程度',
            'joy_words': dutir_dict['乐'][:30],           # 快乐词汇
            'positive_emotion': hownet_dict['正面词'][:30], # 正面情感
            'satisfaction_words': ['满意', '满足', '开心', '愉快', '舒适'],
            'dissatisfaction_words': ['不满', '失望', '沮丧', '烦躁', '厌倦']
        },
        
        'cognitive_engagement': {
            'description': '认知参与度 - 用户的思考深度和学习投入',
            'thinking_words': ['思考', '分析', '理解', '学习', '研究', '探索'],
            'cognitive_verbs': ['认为', '觉得', '发现', '意识到', '领悟', '明白'],
            'learning_words': ['学习', '掌握', '提升', '进步', '成长', '收获'],
            'complexity_indicators': adv_conj_dict.get('连词', [])[:20]  # 连词表示复杂思维
        },
        
        'habit_strength': {
            'description': '习惯强度 - 用户行为的规律性和持续性',
            'routine_words': ['习惯', '经常', '总是', '每天', '定期', '规律'],
            'persistence_words': ['坚持', '持续', '继续', '保持', '维持'],
            'frequency_words': ['频繁', '常常', '反复', '多次', '重复']
        }
    }
    
    print("O变量设计完成！")
    print(f"共设计了 {len(o_variable_mapping)} 个O变量:")
    
    for var_name, var_config in o_variable_mapping.items():
        print(f"\n📊 {var_name}:")
        print(f"   描述: {var_config['description']}")
        print(f"   词汇类别数: {len(var_config) - 1}")  # 减去description
        
        # 显示每个类别的词汇数量
        for category, words in var_config.items():
            if category != 'description' and isinstance(words, list):
                print(f"   - {category}: {len(words)}个词汇")
    
    return o_variable_mapping

def save_o_variable_config(o_variable_mapping):
    """保存O变量配置到文件"""
    print("\n" + "="*50)
    print("💾 保存O变量配置")
    print("="*50)
    
    # 保存为JSON文件
    config_file = 'Desktop/PSF/修改H2/O类变量/词典/O变量词典配置.json'
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(o_variable_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"✅ O变量配置已保存到: {config_file}")
    
    # 创建使用示例
    example_code = '''
# O变量使用示例
import json
import cntext as ct

# 加载O变量配置
with open('O变量词典配置.json', 'r', encoding='utf-8') as f:
    o_config = json.load(f)

# 分析用户评论的O变量特征
def analyze_o_variables(text):
    results = {}
    
    for var_name, var_config in o_config.items():
        var_scores = {}
        
        for category, words in var_config.items():
            if category != 'description' and isinstance(words, list):
                # 计算该类别词汇在文本中的出现次数
                word_count = sum(1 for word in words if word in text)
                var_scores[category] = word_count
        
        results[var_name] = var_scores
    
    return results

# 示例使用
text = "我们团队今天学习了新知识，大家都很开心，我觉得很有收获"
o_features = analyze_o_variables(text)
print(o_features)
'''
    
    example_file = 'Desktop/PSF/修改H2/O类变量/词典/O变量使用示例.py'
    with open(example_file, 'w', encoding='utf-8') as f:
        f.write(example_code)
    
    print(f"✅ 使用示例已保存到: {example_file}")

def main():
    """主函数"""
    print("🚀 开始测试cntext中文心理语言学词典")
    print("="*60)
    
    try:
        # 1. 测试安装
        available_dicts = test_cntext_installation()
        
        # 2. 测试DUTIR情感词典
        dutir_dict = test_dutir_emotion_dict()
        
        # 3. 测试情感分析
        sentiment_results = test_sentiment_analysis()
        
        # 4. 测试其他词典
        test_other_dicts()
        
        # 5. 设计O变量
        o_variable_mapping = design_o_variables()
        
        # 6. 保存配置
        save_o_variable_config(o_variable_mapping)
        
        print("\n" + "="*60)
        print("🎉 所有测试完成！")
        print("✅ cntext词典功能正常")
        print("✅ O变量设计完成")
        print("✅ 配置文件已保存")
        print("\n📋 下一步:")
        print("1. 将O变量分析集成到您的四阈值分析框架中")
        print("2. 为每个用户评论提取O变量特征")
        print("3. 验证O变量与S变量、R变量的关系")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
