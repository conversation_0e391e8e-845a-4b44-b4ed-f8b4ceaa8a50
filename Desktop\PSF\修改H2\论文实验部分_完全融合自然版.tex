\documentclass[12pt,a4paper]{article}
\usepackage[UTF8]{ctex}
\usepackage{amsmath,amsfonts,amssymb}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{longtable}
\usepackage{array}
\usepackage{multirow}
\usepackage{float}
\usepackage{geometry}
\usepackage{setspace}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{hyperref}
\usepackage[table]{xcolor}
\usepackage{threeparttable}
\usepackage{rotating}

\geometry{left=2.5cm,right=2.5cm,top=2.5cm,bottom=2.5cm}
\onehalfspacing

\begin{document}

\section{研究方法}
\label{sec:methodology}

\subsection{四阈值独立实验设计的理论基础与创新}

在用户行为研究中，传统的单时点分析方法往往忽略了用户留存行为的时间动态特征。为了更好地理解用户留存的时间变化规律，本研究采用了四阈值独立实验设计，通过在不同时间点验证SOR理论在用户留存预测中的适用性，探索用户行为的时间演化特征。

四阈值设计将用户留存预测问题分解为四个时间节点的独立实验。通过在90天、150天、180天和330天四个时间点进行分析，可以观察用户行为的时间变化规律。这种设计相比传统的单时点研究，能够更好地理解预测效应的时间变化特征。

四个时间阈值的选择并非任意确定，而是基于严格的数据驱动分析过程。我们开发了多维度综合评分算法，该算法整合了留存率下降程度、活动方差变化和用户分化程度三个核心维度，通过加权计算（权重分别为40\%、30\%、30\%）识别出用户行为模式转换的最优分界点。

\begin{equation}
Threshold\_Score = 0.4 \times Retention\_Drop + 0.3 \times Activity\_Variance + 0.3 \times User\_Differentiation
\end{equation}

通过对365天观察期内每个时间点的系统性评估，我们发现四个关键阈值各自代表了用户生命周期的不同阶段。90天标志着用户从初始适应期向行为分化期的关键转换，此时留存率从理论上的100\%首次显著下降至86.1\%，反映了用户对平台价值认知的初步形成。150天则代表了用户流失的高峰期，单期流失率达到13.9\%的最高水平，这一阶段用户面临着是否继续深度参与的关键决策。180天阈值体现了用户承诺决策的分水岭，此时出现明显的"幸存者效应"，留存用户开始表现出更强的平台依赖性。330天则确认了长期稳定模式的建立，留存率趋于稳定，用户行为模式基本固化。

\begin{table}[htbp]
\centering
\caption{四阈值选择的数据驱动依据与用户行为特征}
\label{tab:threshold_selection_comprehensive}
\begin{threeparttable}
\begin{tabular}{lcccccc}
\toprule
阈值 & 留存率(\%) & 流失率变化(\%) & 行为方差 & 综合得分 & 用户状态 & 心理特征 \\
\midrule
90天 & 86.1 & -13.9 & 2.34 & 0.847 & 分化期 & 价值认知形成 \\
150天 & 72.2 & -13.9 & 3.12 & 0.923 & 决策期 & 承诺意愿评估 \\
180天 & 68.8 & -3.4 & 2.89 & 0.756 & 承诺期 & 幸存者效应显现 \\
330天 & 65.4 & -3.4 & 1.67 & 0.612 & 稳定期 & 行为模式固化 \\
\bottomrule
\end{tabular}
\begin{tablenotes}
\item 注：综合得分基于三个维度的加权平均，用户状态和心理特征基于行为数据的深度分析
\end{tablenotes}
\end{threeparttable}
\end{table}

\subsection{扩展SOR理论框架的构建与验证}

SOR（Stimulus-Organism-Response）理论在消费者行为研究中应用广泛。本研究将SOR理论应用到用户留存预测领域，探索刺激变量如何通过心理机制影响用户留存行为。在传统SOR模型基础上，本研究考虑了时间维度的影响，分析不同心理机制在用户留存中的作用。

本研究发现用户心理反应具有双重性质。同一社交刺激可能通过不同心理机制产生不同的行为效应。社交互动刺激既可能通过提升用户的社交效能感产生正向效应，也可能通过影响用户的情感稳定性产生负向效应。

\begin{align}
\text{传统SOR模型：} & \quad S \rightarrow O \rightarrow R \\
\text{四阈值扩展模型：} & \quad S \rightarrow O_1^{(t)} \rightarrow R^{(t)} \quad \text{(正向激励路径)} \\
& \quad S \rightarrow O_2^{(t)} \rightarrow R^{(t)} \quad \text{(负向压力路径)} \\
& \quad R_{net}^{(t)} = \alpha \cdot R_1^{(t)} + \beta \cdot R_2^{(t)} + \gamma \cdot R_{direct}^{(t)} \\
\text{其中：} & \quad t \in \{90, 150, 180, 330\} \text{天}
\end{align}

双路径机制的发现有一定的理论意义。正向激励路径体现了社交互动通过增强用户自信心促进持续参与的机制，这与社会认知理论的预测一致。负向压力路径则显示了数字社交环境的复杂性——过度的社交关注可能增加用户的心理压力，产生回避行为。这一发现有助于理解数字用户行为的复杂性。

\subsection{变量构建与测量的创新方法}

\subsubsection{刺激变量的多维度操作化}

基于扩展SOR理论框架和用户留存相关文献的系统性回顾，本研究构建了涵盖四个核心维度的刺激变量体系。

\textbf{社交互动频率变量}通过量化用户在平台上的各类社交行为，全面反映了用户社交活跃程度。为了处理社交媒体数据典型的右偏分布特征，所有计数变量均采用对数变换处理：

\begin{align}
early\_activity\_log &= \log(1 + early\_activity\_count) \\
total\_interactions\_log &= \log(1 + total\_interactions\_count) \\
received\_comments\_log &= \log(1 + received\_comments\_count)
\end{align}

其中$early\_activity\_count$表示用户前30天的活动次数，$total\_interactions\_count$为用户总互动次数，$received\_comments\_count$为用户收到的评论总数。对数变换中的"+1"处理确保了零值的合理处理。

\textbf{网络中心性变量}的构建基于复杂网络理论，通过分析用户在社交网络中的位置和影响力计算得出：

\begin{align}
degree\_centrality &= \frac{degree(v)}{n-1} \\
closeness\_centrality &= \frac{n-1}{\sum_{u \in V} d(v,u)} \\
betweenness\_centrality &= \sum_{s \neq v \neq t} \frac{\sigma_{st}(v)}{\sigma_{st}} \\
pagerank &= \frac{1-d}{n} + d \sum_{u \in M(v)} \frac{PR(u)}{L(u)}
\end{align}

其中$degree(v)$为节点$v$的度数，$n$为网络节点总数，$d(v,u)$为节点$v$和$u$之间的最短路径距离，$\sigma_{st}(v)$为经过节点$v$的从$s$到$t$的最短路径数，$d$为阻尼系数（通常取0.85），$M(v)$为指向节点$v$的节点集合。

\textbf{反馈存在性变量}采用二元编码方式：

\begin{equation}
has\_received\_comments = \begin{cases}
1, & \text{如果 } received\_comments\_count > 0 \\
0, & \text{如果 } received\_comments\_count = 0
\end{cases}
\end{equation}

这一多维度的变量构建策略不仅确保了对用户社交刺激的全面捕捉，更重要的是为后续的四阈值对比分析提供了坚实的数据基础。

\subsubsection{机体变量的创新构建方法}

机体变量（O变量）的构建是本研究的重要方法学创新。与传统研究依赖问卷调查获取用户心理状态数据的方法不同，我们基于心理语言学理论，通过先进的文本挖掘技术从用户的自然语言表达中提取心理状态指标。这种方法不仅避免了问卷调查的主观偏差和社会期望效应，更重要的是能够获取用户真实的、动态的心理状态数据。

社交效能感（Social Efficacy）的计算基于大连理工大学情感词汇本体库，结合社交效能感理论的核心要素进行构建。计算过程综合考虑了词汇权重、词汇频次和语境调节系数三个关键因素，通过加权平均的方式得出最终得分。

\begin{equation}
Social\_Efficacy = \frac{\sum_{i=1}^{n} w_i \cdot f_i \cdot c_i}{\sum_{i=1}^{n} f_i}
\end{equation}

其中$w_i$代表第$i$个词汇的情感权重，$f_i$表示该词汇在用户评论中的出现频次，$c_i$为基于上下文语境确定的调节系数。这一计算方法能够准确反映用户在社交互动中的自信程度和效能感知水平。

情感稳定性（Emotional Stability）的构建则基于CLIWC（Computerized Language Inquiry and Word Count）中文词典，通过量化用户情感表达的稳定性来反映其面对社交压力时的心理韧性。

\begin{equation}
Emotional\_Stability = 1 - \sqrt{\frac{\sum_{i=1}^{n}(e_i - \bar{e})^2}{n-1}}
\end{equation}

其中$e_i$表示用户第$i$次评论的情感得分，$\bar{e}$为该用户所有评论的平均情感得分。该指标通过计算情感得分的标准差并进行反向转换，数值越高表示用户情感表达越稳定，心理韧性越强。
O变量的构建过程包括文本预处理、情感词典匹配、权重计算、语境分析和标准化处理五个关键步骤。最终实现了91.57\%的用户匹配率，为后续的四阈值分析提供了可靠的心理状态数据支撑。

\subsubsection{O变量构建的技术细节与验证}

为确保O变量构建的科学性和可靠性，我们采用了多重验证策略。首先，通过内部一致性分析验证构建的可靠性。我们计算了构成每个O变量的词汇子集之间的Cronbach's α系数。Social\_Efficacy相关词汇的α系数为0.84，Emotional\_Stability相关词汇的α系数为0.79，均超过了0.70的可接受阈值，表明内部一致性良好。

其次，通过时间稳定性分析验证变量的一致性。我们分析了用户在不同时间窗口内的O变量得分变化，发现Social\_Efficacy\_score和Emotional\_Stability\_score在短期内（7-14天）表现出较好的稳定性，变异系数分别为0.12和0.15，表明基于文本挖掘构建的心理变量具有合理的时间一致性。

最后，通过理论一致性检验验证构建效度。我们将构建的O变量与相关的行为指标进行相关性分析，发现Social\_Efficacy与用户的主动互动行为呈显著正相关（r=0.45, p<0.001），Emotional\_Stability与用户的持续参与行为呈显著正相关（r=0.38, p<0.001），这些结果与理论预期高度一致，支持了变量构建的效度。

\subsubsection{四阈值数据质量的系统性评估}

为确保四阈值分析的数据质量，我们进行了全面的数据质量评估。首先是缺失值分析，发现行为变量和网络变量的缺失率均低于1\%，主要集中在新注册用户的早期数据。O变量的缺失率为8.43\%，主要原因是部分用户缺乏足够的评论文本进行情感分析。我们采用多重插补法处理缺失值，通过构建预测模型基于用户的其他特征进行插补。

异常值检测采用了多种方法的组合策略。对于连续变量，我们使用箱线图法识别超出1.5倍四分位距的极端值；对于网络变量，考虑到其幂律分布特征，采用基于对数变换后的Z-score方法；对于O变量，结合专业知识设定合理的取值范围。最终识别出的异常值比例为2.3\%，经过仔细检查后保留了具有合理解释的极端值，删除了明显的数据错误。

数据分布特征的分析显示，大部分变量经过适当变换后接近正态分布。行为变量经对数变换后偏度系数降至-0.5到0.5之间；网络变量由于其固有的幂律特征，即使经过变换仍保持一定的右偏，但在可接受范围内；O变量表现出良好的正态性，偏度和峰度系数均在±1.0范围内。

\subsection{统计分析策略与时间敏感性方法}

本研究采用多层次统计分析框架，确保研究结果的稳健性和可靠性。分析策略的设计充分考虑了四阈值数据的特殊性质，通过系统性的统计检验和效应量计算，实现了对用户留存预测机制的全面验证。

第一层分析聚焦于描述性统计与数据质量检验，通过Kolmogorov-Smirnov检验评估各变量的分布特征，对显著偏离正态分布的变量采用适当的变换方法。第二层分析采用置换检验（permutation test）评估统计显著性，通过10,000次随机重采样构建零假设分布，并使用Bonferroni方法进行多重比较校正，严格控制家族错误率在0.05水平。第三层分析运用Bootstrap方法进行中介效应分析，通过5,000次重采样构建偏差校正置信区间。第四层分析采用随机森林算法构建预测模型，通过10折交叉验证评估模型性能。

为了系统性评估效应的时间稳定性，我们开发了专门的时间敏感性分析方法。效应衰减率通过比较90天和330天阈值的效应大小差异来量化，稳定性指数则通过计算四个阈值间效应大小的变异系数来评估。

\begin{align}
Decay\_Rate &= \frac{d_{90} - d_{330}}{d_{90}} \times 100\% \\
Stability\_Index &= 1 - \frac{SD(d_1, d_2, d_3, d_4)}{Mean(d_1, d_2, d_3, d_4)}
\end{align}

\section{实验结果}
\label{sec:results}

\subsection{四阈值用户行为演化的描述性分析}

四阈值数据集的描述性统计分析揭示了用户行为变量随时间演化的典型特征和内在规律。通过对2,159名用户在四个时间阈值下的行为数据进行系统性分析，我们发现用户行为变量呈现出明显的时间依赖性特征，这一发现为后续的效应分析提供了重要的数据基础。

从整体样本特征来看，用户留存率随时间阈值的增加而逐步下降，从90天的86.1\%降至330天的65.4\%，呈现出典型的时间衰减模式。这一衰减过程并非线性的，而是表现出明显的阶段性特征：90天至150天期间流失率增长最为显著（从13.9\%增至27.8\%），150天至180天期间增长速度放缓，180天至330天期间则进入相对稳定的缓慢增长阶段。

\begin{table}[htbp]
\centering
\caption{主要变量四阈值描述性统计与演化特征分析}
\label{tab:descriptive_stats_evolution}
\begin{threeparttable}
\begin{tabular}{lcccccc}
\toprule
\multirow{2}{*}{变量类别} & \multirow{2}{*}{具体变量} & \multicolumn{4}{c}{均值 (标准差)} & \multirow{2}{*}{演化趋势} \\
\cmidrule{3-6}
& & 90天 & 150天 & 180天 & 330天 & \\
\midrule
\multirow{3}{*}{行为变量} & active\_months & 8.42 (4.67) & 8.38 (4.65) & 8.35 (4.63) & 8.21 (4.58) & 渐进衰减 \\
& total\_interactions\_log & 3.84 (1.92) & 3.81 (1.90) & 3.79 (1.89) & 3.72 (1.85) & 线性下降 \\
& received\_comments\_log & 2.16 (1.84) & 2.13 (1.82) & 2.11 (1.81) & 2.05 (1.77) & 稳定下降 \\
\midrule
\multirow{4}{*}{网络变量} & degree\_centrality & 0.0023 (0.0089) & 0.0022 (0.0087) & 0.0021 (0.0085) & 0.0019 (0.0081) & 持续衰减 \\
& pagerank & 0.00046 (0.00087) & 0.00044 (0.00084) & 0.00043 (0.00082) & 0.00039 (0.00078) & 指数衰减 \\
& closeness\_centrality & 0.0156 (0.0234) & 0.0152 (0.0231) & 0.0149 (0.0228) & 0.0142 (0.0221) & 缓慢下降 \\
& betweenness\_centrality & 0.0008 (0.0045) & 0.0007 (0.0043) & 0.0007 (0.0042) & 0.0006 (0.0039) & 阶梯下降 \\
\midrule
\multirow{2}{*}{心理变量} & Social\_Efficacy\_score & 0.52 (0.28) & 0.51 (0.28) & 0.51 (0.28) & 0.50 (0.27) & 微幅下降 \\
& Emotional\_Stability\_score & 0.48 (0.31) & 0.48 (0.31) & 0.47 (0.30) & 0.47 (0.30) & 相对稳定 \\
\midrule
\multirow{3}{*}{样本特征} & 留存用户数 & 1,859 & 1,559 & 1,485 & 1,412 & 递减 \\
& 流失用户数 & 300 & 600 & 674 & 747 & 递增 \\
& 留存率 (\%) & 86.1 & 72.2 & 68.8 & 65.4 & 下降 \\
\bottomrule
\end{tabular}
\begin{tablenotes}
\item 注：演化趋势基于四个时间点的变化模式分析，留存率 = 留存用户数/(留存用户数+流失用户数) × 100\%
\item 样本数据与表\ref{tab:threshold_selection_comprehensive}中的阈值选择数据保持一致
\end{tablenotes}
\end{threeparttable}
\end{table}

行为变量的时间演化呈现出一致的下降趋势，但下降速度存在显著差异。用户活跃月数（active\_months）表现出最为稳定的渐进衰减模式，从90天的8.42个月缓慢下降至330天的8.21个月，变化幅度仅为2.5\%。这一相对稳定性反映了用户参与时长作为核心行为指标的内在稳定性。相比之下，总互动次数和收到评论数的对数变换值表现出更为明显的线性下降趋势，这可能反映了随着时间推移，活跃用户群体的逐步缩小对整体互动水平的影响。

网络变量的演化模式更加复杂多样。度中心性和PageRank值表现出持续的衰减趋势，这与用户网络地位的时间敏感性特征相符。特别值得注意的是，PageRank值呈现出近似指数衰减的模式，从90天的0.00046下降至330天的0.00039，衰减幅度达到15.2\%。这一发现表明，用户在网络中的影响力具有明显的时间衰减特征，需要持续的活跃参与来维持。

心理变量的演化模式与行为变量和网络变量形成了鲜明对比。社交效能感和情感稳定性在四个时间阈值间表现出相对稳定的特征，变异系数均小于5\%。这一稳定性可能反映了用户心理特征的内在一致性，也可能暗示心理变量对时间变化的相对不敏感性。然而，这种稳定性并不意味着心理变量在用户留存预测中的作用不重要，相反，它们可能通过更加复杂的中介机制发挥作用。

进一步的聚类分析识别了五种典型的用户行为模式，包括持续活跃型（23.4\%）、早期衰减型（31.7\%）、渐进增长型（18.9\%）、波动不稳型（15.2\%）和低参与型（10.8\%）。用户状态转换分析显示，从90天到150天的转换中，高留存用户的稳定率为78.2\%，中留存用户的流动性更大，低留存用户中有72.3\%保持低留存状态。

\subsection{四阈值主效应分析}

本节旨在对研究的基础性假设(H1)进行检验，即评估核心预测因子的基础效应与时间动态。如表\ref{tab:main_effects_comprehensive_analysis}所示，分析结果为H1提供了全面的实证支持。首先，所有核心刺激变量（S）与机体变量（O）均对用户留存表现出显著的预测效应，这验证了H1a和H1b。其次，数据明确地支持了H1c，所有变量的预测效应均呈现清晰的时间衰减模式，总体平均衰减率达到37.8\%，这从实证上确立了本研究采用多阈值设计的必要性。

\begin{table}[htbp]
\centering
\caption{四阈值主效应分析：效应大小、时间稳定性与理论意义}
\label{tab:main_effects_comprehensive_analysis}
\begin{threeparttable}
\begin{tabular}{lccccccccc}
\toprule
\multirow{2}{*}{变量} & \multicolumn{4}{c}{Cohen's d 效应大小} & \multirow{2}{*}{平均效应} & \multirow{2}{*}{衰减率(\%)} & \multirow{2}{*}{稳定性指数} & \multirow{2}{*}{效应分类} & \multirow{2}{*}{理论意义} \\
\cmidrule{2-5}
& 90天 & 150天 & 180天 & 330天 & & & & & \\
\midrule
active\_months & 2.52*** & 2.27*** & 2.15*** & 1.43*** & 2.09 & 43.4 & 0.847 & 超大效应 & 时间投入理论 \\
degree\_centrality & 1.61*** & 1.42*** & 1.32*** & 0.89*** & 1.31 & 44.6 & 0.723 & 超大效应 & 网络嵌入理论 \\
received\_comments\_log & 1.53*** & 1.33*** & 1.27*** & 0.96*** & 1.27 & 37.3 & 0.782 & 超大效应 & 社交反馈理论 \\
total\_interactions\_log & 1.46*** & 1.30*** & 1.26*** & 0.90*** & 1.23 & 38.3 & 0.769 & 超大效应 & 参与投入理论 \\
pagerank & 1.13*** & 0.99*** & 0.90*** & 0.65*** & 0.92 & 42.3 & 0.698 & 大效应 & 影响力理论 \\
closeness\_centrality & 1.10*** & 1.01*** & 0.99*** & 0.84*** & 0.98 & 23.5 & 0.856 & 大效应 & 网络接近性理论 \\
betweenness\_centrality & 0.90*** & 0.74*** & 0.64*** & 0.48*** & 0.69 & 46.2 & 0.612 & 大效应 & 信息中介理论 \\
has\_received\_comments & 0.78*** & 0.71*** & 0.72*** & 0.65*** & 0.71 & 16.8 & 0.891 & 中等效应 & 社交认可理论 \\
Social\_Efficacy & 0.55*** & 0.53*** & 0.54*** & 0.45*** & 0.52 & 18.3 & 0.823 & 中等效应 & 自我效能理论 \\
early\_activity\_log & 0.36* & 0.28 & 0.24 & 0.16 & 0.26 & 54.7 & 0.445 & 小效应 & 早期投入理论 \\
Emotional\_Stability & 0.19 & 0.18* & 0.16 & 0.16* & 0.17 & 18.7 & 0.734 & 小效应 & 情感稳定理论 \\
\bottomrule
\end{tabular}
\begin{tablenotes}
\item 注：***p < 0.001, **p < 0.01, *p < 0.05（Bonferroni校正后）；稳定性指数 = 1 - CV
\item 效应分类：超大效应(d≥1.2)、大效应(0.8≤d<1.2)、中等效应(0.5≤d<0.8)、小效应(0.2≤d<0.5)
\end{tablenotes}
\end{threeparttable}
\end{table}

如表\ref{tab:main_effects_comprehensive_analysis}所示，分析结果为我们的基础假设提供了较为有力的支持。值得注意的是active\_months变量展现出的超大效应（平均d=2.09），这一结果表明用户在平台上的时间投入对其留存决策可能具有重要影响。尽管该变量存在显著的时间衰减（从2.52降至1.43，衰减率43.4\%），但其稳定性指数仍达到0.847，这提示时间投入的影响虽会减弱，但在一定程度上仍保持着相当的预测力。

社交互动和网络嵌入的重要性同样得到了一定程度的验证。四个网络中心性变量占据了前七位中的四席，其中degree\_centrality（d=1.31）和received\_comments\_log（d=1.27）的超大效应值得关注。这些发现提示了一个可能的理论洞察：用户在社交网络中的结构位置和获得的社交反馈，可能不仅是其社交活跃度的体现，也可能是其平台归属感和价值认同的重要来源。值得注意的是，不同网络指标表现出了差异化的时间特征——closeness\_centrality展现出相对较高的稳定性（稳定性指数0.856），而betweenness\_centrality则衰减相对迅速（衰减率46.2\%），这可能暗示了不同类型的网络优势在时间维度上的不同持久性。

心理状态变量的表现则呈现出相对复杂的图景。Social\_Efficacy在四个阈值下均保持稳定的中等效应水平（平均d=0.52，稳定性指数0.823），这在一定程度上支持了社交效能感作为内在心理驱动力的可能作用。相比之下，Emotional\_Stability的直接效应相对较小且呈现波动模式，这一发现提示情感稳定性可能更多地通过间接路径影响用户行为，而非产生直接的留存效应。

值得关注的是所有变量都表现出的系统性时间衰减模式。总体平均衰减率达到37.8\%，这一发现为用户行为预测的"时效性"提供了有力的支持性证据。特别值得关注的是，early\_activity\_log虽然平均效应较小（d=0.26），但其54.7\%的衰减率提示了早期行为信号可能具有较高的时间敏感性，而has\_received\_comments仅16.8\%的缓慢衰减则可能表明社交认可具有相对持久的影响力。

\subsection{双路径中介效应分析结果}

在确立了基础效应之后，我们继而深入探究其内在机制，以检验本研究的核心理论贡献——双路径中介机制假设(H2)。表\ref{tab:mediation_effects_comprehensive}的Bootstrap中介效应分析结果，为这一双路径模型的存在提供了较为有力的支持性证据，较为清晰地显示了\textbf{正向激励(H2a)与负向压力(H2b)}两条路径可能并存的复杂机制。

\begin{table}[htbp]
\centering
\caption{四阈值双路径中介效应：正向激励与负向压力的动态平衡}
\label{tab:mediation_effects_comprehensive}
\begin{threeparttable}
\begin{tabular}{lcccccccccc}
\toprule
\multirow{3}{*}{刺激变量} & \multicolumn{5}{c}{Social\_Efficacy中介效应(\%)} & \multicolumn{5}{c}{Emotional\_Stability中介效应(\%)} \\
\cmidrule(lr){2-6} \cmidrule(lr){7-11}
& 90天 & 150天 & 180天 & 330天 & 平均 & 90天 & 150天 & 180天 & 330天 & 平均 \\
\midrule
\multicolumn{11}{l}{\textbf{强中介效应路径（|效应|>15\%）}} \\
early\_activity & 49.2*** & 51.6*** & 52.8*** & 56.6*** & 52.6 & 6.6** & 5.8* & 5.2* & 4.1 & 5.4 \\
pagerank & 21.4*** & 20.8*** & 19.6*** & 18.2*** & 20.0 & -2.2* & -2.4* & -2.1* & -1.8 & -2.1 \\
has\_received\_comments & 18.9*** & 17.6*** & 16.8*** & 15.3*** & 17.2 & -4.8*** & -4.7*** & -4.6*** & -5.0*** & -4.8 \\
\midrule
\multicolumn{11}{l}{\textbf{中等中介效应路径（5\%≤|效应|<15\%）}} \\
received\_comments\_log & 11.8*** & 11.2*** & 10.9*** & 10.1*** & 11.0 & -3.0** & -2.8** & -2.6* & -2.9** & -2.8 \\
degree\_centrality & 12.6*** & 11.9*** & 11.4*** & 10.7*** & 11.7 & 0.1 & 0.3 & 0.2 & -0.1 & 0.1 \\
total\_interactions\_log & 11.6*** & 11.1*** & 10.8*** & 10.2*** & 10.9 & 1.2 & 1.0 & 0.8 & 0.6 & 0.9 \\
closeness\_centrality & 11.6*** & 11.2*** & 10.9*** & 10.4*** & 11.0 & -1.8* & -1.6* & -1.5 & -1.3 & -1.6 \\
betweenness\_centrality & 11.8*** & 10.9*** & 10.2*** & 9.1*** & 10.5 & -1.6* & -1.4 & -1.2 & -1.0 & -1.3 \\
active\_months & 8.3*** & 8.0*** & 7.8*** & 7.2*** & 7.8 & 0.8 & 0.6 & 0.5 & 0.3 & 0.6 \\
\midrule
\multicolumn{11}{l}{\textbf{跨阈值统计摘要}} \\
显著正向路径数 & 9/9 & 9/9 & 9/9 & 9/9 & - & 1/9 & 1/9 & 1/9 & 0/9 & - \\
显著负向路径数 & 0/9 & 0/9 & 0/9 & 0/9 & - & 5/9 & 5/9 & 4/9 & 4/9 & - \\
平均正向效应 & 17.5 & 17.1 & 16.8 & 16.4 & 17.0 & - & - & - & - & - \\
平均负向效应 & - & - & - & - & - & -2.3 & -2.2 & -2.0 & -2.2 & -2.2 \\
净中介效应 & 17.5 & 17.1 & 16.8 & 16.4 & 17.0 & -2.3 & -2.2 & -2.0 & -2.2 & -2.2 \\
\bottomrule
\end{tabular}
\begin{tablenotes}
\item 注：***p < 0.001, **p < 0.01, *p < 0.05；中介效应百分比 = (a×b)/c × 100\%
\item 负值表示负向中介，正值表示正向中介；净中介效应 = 正向效应 + 负向效应
\end{tablenotes}
\end{threeparttable}
\end{table}

中介效应分析显示了一个值得关注的发现：在本研究样本中，用户留存的驱动机制可能比表面现象更为复杂，可能存在着两条相对不同且并行运作的心理路径。值得注意的是early\_activity → Social\_Efficacy → User\_Retention路径，它展现出相对较高的中介效应强度（平均52.6\%），并且表现出随时间增强的模式——从90天的49.2\%增至330天的56.6\%。这一发现可能提示了用户早期积极参与的一种可能机制：通过构建社交自信心和价值感，可能形成一种自我强化的正向循环，产生相对持续的留存驱动力。

这种"复利效应"在网络地位变量中也得到了一定程度的体现。degree\_centrality和pagerank通过Social\_Efficacy产生的显著正向中介效应（分别为15.2\%和20.0\%），在一定程度上支持了网络中心性可能通过增强用户社交自信心来促进留存的理论机制。这些发现可能共同描绘了一个相对清晰的图景：社交刺激可能通过增强用户的价值感和归属感，创造出一种相对内在的、可持续的留存动力。

然而，分析同时显示了数字社交环境可能存在的内在矛盾性。has\_received\_comments → Emotional\_Stability → User\_Retention路径在四个时间阈值下均呈现稳定的负向中介效应（-4.8\%至-5.0\%，变异系数仅3.2\%），这一发现提示过度的社交关注可能通过影响用户的情感稳定性而产生一定的回避行为。值得关注的洞察来自pagerank变量的双重效应：它既通过Social\_Efficacy产生正向中介效应（20.0\%），又通过Emotional\_Stability产生负向中介效应（-2.1\%），净效应为17.9\%。这种同一刺激可能激活相反心理机制的现象，较好地体现了数字社交环境的复杂性——社交关注既可能是自信的源泉，也可能成为压力的来源。

整体而言，正向中介效应的平均水平（17.0\%）显著高于负向中介效应（-2.2\%），表明尽管存在负向压力路径，但正向激励路径仍占据主导地位。稳健性检验通过Bootstrap方法（10,000次重采样）验证了所有中介效应的可靠性，确保了研究结论的统计稳健性。

\subsection{四阈值调节效应分析}

最后，为了探究双路径机制的适用边界，本研究检验了\textbf{用户经验水平的调节作用假设(H3)}。我们基于用户活跃月数将样本分为高、低经验组，旨在验证不同经验水平是否会改变核心预测因子的影响力。

\begin{table}[htbp]
\centering
\caption{四阈值用户经验水平调节效应综合分析}
\label{tab:moderation_comprehensive_analysis}
\begin{threeparttable}
\begin{tabular}{lcccccccc}
\toprule
\multirow{2}{*}{预测变量} & \multirow{2}{*}{用户组别} & \multirow{2}{*}{样本数} & \multicolumn{4}{c}{效应强度(Cohen's d)} & \multirow{2}{*}{平均效应} & \multirow{2}{*}{显著性} \\
\cmidrule(lr){4-7}
& & & 90天 & 150天 & 180天 & 330天 & & \\
\midrule
\multirow{3}{*}{early\_activity} & 低经验组 & 1,267 & 1.89 & 1.76 & 1.68 & 1.52 & 1.71 & *** \\
& 高经验组 & 892 & 1.23 & 1.15 & 1.09 & 0.98 & 1.11 & *** \\
& 调节效应 & - & -34.9\% & -34.7\% & -35.1\% & -35.5\% & -35.1\% & ** \\
\midrule
\multirow{3}{*}{degree\_centrality} & 低经验组 & 1,267 & 1.34 & 1.21 & 1.15 & 1.02 & 1.18 & *** \\
& 高经验组 & 892 & 0.89 & 0.82 & 0.78 & 0.71 & 0.80 & *** \\
& 调节效应 & - & -33.6\% & -32.2\% & -32.2\% & -30.4\% & -32.1\% & ** \\
\midrule
\multirow{3}{*}{pagerank} & 低经验组 & 1,267 & 1.12 & 1.05 & 0.98 & 0.89 & 1.01 & *** \\
& 高经验组 & 892 & 0.78 & 0.73 & 0.69 & 0.62 & 0.71 & *** \\
& 调节效应 & - & -30.4\% & -30.5\% & -29.6\% & -30.3\% & -30.2\% & ** \\
\midrule
\multirow{3}{*}{has\_received\_comments} & 低经验组 & 1,267 & 0.45 & 0.42 & 0.39 & 0.35 & 0.40 & ** \\
& 高经验组 & 892 & 0.67 & 0.63 & 0.59 & 0.54 & 0.61 & *** \\
& 调节效应 & - & +48.9\% & +50.0\% & +51.3\% & +54.3\% & +51.1\% & ** \\
\midrule
\multirow{3}{*}{Social\_Efficacy} & 低经验组 & 1,267 & 0.52 & 0.48 & 0.45 & 0.41 & 0.47 & ** \\
& 高经验组 & 892 & 0.34 & 0.32 & 0.29 & 0.26 & 0.30 & * \\
& 调节效应 & - & -34.6\% & -33.3\% & -35.6\% & -36.6\% & -35.0\% & * \\
\bottomrule
\end{tabular}
\begin{tablenotes}
\footnotesize
\item 注：*** p<0.001, ** p<0.01, * p<0.05
\item 调节效应 = (高经验组效应 - 低经验组效应) / 低经验组效应 × 100\%
\item 低经验组：活跃月数≤8个月；高经验组：活跃月数>8个月
\item 调节效应为负表示高经验组的效应弱于低经验组，为正表示高经验组效应强于低经验组
\end{tablenotes}
\end{threeparttable}
\end{table}

调节效应分析显示了一个值得关注的现象：在本研究样本中，驱动用户留存的心理机制可能并非一成不变，而是可能会随着用户经验的积累发生一定程度的演化。值得注意的是early\_activity、degree\_centrality、pagerank和Social\_Efficacy等变量在新用户中均表现出相对更强的效应，调节效应分别达到-35.1\%、-32.1\%、-30.2\%和-35.0\%。这一相对系统性的模式可能提示新用户面临着一个可能的挑战：如何在相对不确定的数字环境中找到自己的位置。

对于刚进入平台的用户而言，每一次早期活动可能都是对未知环境的探索，每一个网络连接可能都是减少孤独感的尝试，每一点社交效能感的提升可能都是对自身适应能力的确认。这种"外部导航"模式可能反映了新用户通过积极的社交投入来削减环境不确定性的一种策略。他们可能需要通过外部线索来理解平台规范、建立社交期望、评估行为后果，因此可能对这些早期信号表现出相对更高的敏感性。

然而，当我们将目光转向经验丰富的用户时，发现了一个相对不同的图景。has\_received\_comments在老用户中表现出相对更强的效应（调节效应+51.1\%），这一发现与其他变量的模式形成了一定的对比。对于已经积累了一定平台经验和社交资本的老用户来说，他们的关注点可能已经从"如何建立连接"转向了"如何维持地位"。来自他人的评论和关注可能不再仅仅是社交互动的表现，也可能成为了他们评估自身社会影响力和地位认可的重要信号。

这种从"外部导航"到"反馈敏感"的转变，可能提示了用户留存驱动机制的一种演化规律。在本研究样本中，新用户的行为可能更多地受到不确定性削减的驱动，而老用户的行为则可能更多地受到社会资本维系的影响。这可能不仅是量的变化，也可能是质的转换，在一定程度上反映了用户需求从"适应环境"到"维持地位"的演进。

\subsection{四阈值预测模型性能分析}

为了综合评估基于本研究假设框架所识别出的多维度预测因子的整体效能，我们构建了随机森林预测模型。采用随机森林算法构建预测模型，通过10折交叉验证评估四个时间阈值下的预测性能。

\begin{table}[htbp]
\centering
\caption{四阈值预测模型性能：精度、平衡性与应用价值的综合评估}
\label{tab:model_performance_detailed}
\begin{threeparttable}
\begin{tabular}{lcccccccc}
\toprule
时间阈值 & AUC & Accuracy & Precision & Recall & F1-Score & 样本平衡比 & 应用场景 & 推荐指数 \\
\midrule
90天 & 0.8383 & 0.823 & 0.856 & 0.789 & 0.821 & 21.7:1 & 精准预警 & ⭐⭐⭐⭐⭐ \\
150天 & 0.7933 & 0.789 & 0.812 & 0.756 & 0.783 & 9.6:1 & 早期干预 & ⭐⭐⭐⭐ \\
180天 & 0.8038 & 0.798 & 0.823 & 0.767 & 0.794 & 7.1:1 & 规模应用 & ⭐⭐⭐⭐⭐ \\
330天 & 0.7662 & 0.756 & 0.789 & 0.712 & 0.748 & 4.1:1 & 长期规划 & ⭐⭐⭐ \\
\midrule
性能指标统计 & & & & & & & & \\
平均值 & 0.7954 & 0.792 & 0.820 & 0.756 & 0.787 & - & - & - \\
标准差 & 0.0301 & 0.028 & 0.028 & 0.032 & 0.031 & - & - & - \\
变异系数 & 0.038 & 0.035 & 0.034 & 0.042 & 0.039 & - & - & - \\
最优阈值 & 90天 & 90天 & 90天 & 90天 & 90天 & 330天 & - & 90天/180天 \\
\bottomrule
\end{tabular}
\begin{tablenotes}
\item 注：所有指标基于10折交叉验证；推荐指数基于性能与应用价值的综合评估
\item 样本平衡比 = 留存用户数/流失用户数，比值越小表示样本越平衡
\end{tablenotes}
\end{threeparttable}
\end{table}

如表\ref{tab:model_performance_detailed}所示，90天阈值在所有性能指标上表现最佳，AUC达到0.8383，精确率为0.856。但该阈值面临样本不平衡问题（21.7:1）。180天阈值在性能与样本平衡性之间达到较好权衡，AUC为0.8038，样本平衡比为7.1:1。

330天阈值的性能下降（AUC=0.7662）反映了长期预测的困难，但在样本平衡性方面表现最佳（4.1:1）。

时间衰减模式分析显示，不同变量呈现指数衰减型、线性衰减型、平台衰减型和波动稳定型四种典型模式。其中PageRank等网络变量表现为指数衰减，互动变量表现为线性衰减，心理变量相对稳定。



\begin{figure}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{figures/roc_curves_comparison.png}
\caption{四阈值预测模型ROC曲线对比}
\label{fig:roc_curves}
\end{figure}

如图\ref{fig:roc_curves}所示，90天阈值的AUC达到0.8383，为最优预测性能；150天和180天阈值的AUC分别为0.7933和0.8038；330天阈值的AUC为0.7662。

模型验证通过10折交叉验证确认了结果的稳健性，平均AUC为0.7954±0.0301。













\section{讨论}
\label{sec:discussion}

本研究通过一个动态的多阈值实验设计，系统性地检验了一个关于用户留存的整合性理论框架。我们的发现在一定程度上支持了预设的三个核心假设，并可能为理解数字环境下的用户行为复杂性提供了一些有益的洞见。

\subsection{核心发现的整合性阐释}

本研究的核心发现可以被整合为一个相对统一的叙事：在本研究样本中，用户的留存决策可能是一个动态演化的、由双重心理机制驱动的、且受个体经验调节的相对复杂的过程。首先，我们的结果在一定程度上支持了用户行为预测的"时效性"，即所有预测因子的影响力均随时间衰减(H1)，这可能凸显了用户生命周期早期的重要性。其次，我们发现了可能驱动这一过程的内在机制：一个由社交自信驱动的\textbf{"正向激励"路径}，与一个由社交压力驱动的\textbf{"负向压力"路径}可能并行存在(H2)。值得关注的是，我们发现这两条路径的相对强度可能并非一成不变，而是可能受到用户经验水平的调节(H3)：新用户可能更依赖正向激励路径来构建归属感（"新手导航效应"），而老用户则可能对社交反馈更为敏感（"老手敏感效应"）。

\subsection{理论贡献}

本研究的发现可能对现有理论做出了一定的贡献。第一，我们通过发现负向中介路径和调节效应，尝试将传统的SOR模型从一个静态、线性的框架，扩展为了一个可能能够解释内在矛盾和动态演化的\textbf{"动态情境化SOR模型"}。这可能在一定程度上增强了SOR理论在复杂数字环境中的解释力。第二，我们的发现可能为社会资本理论和不确定性削减理论提供了一些实证证据和情境化理解。"新手导航效应"的发现，可能展示了不确定性削减是新用户社交行为的一个重要动机；而"老手敏感效应"则可能提示了社会资本的维系，而非建立，可能是老用户参与行为的一个重要驱动力。





\subsection{实践意义}

本研究的发现可能对用户留存管理具有一定的实践指导价值。首先，90天作为预测和干预的相对重要时期(H1的启示)，可能提示管理者应考虑将更多资源向用户生命周期的早期阶段倾斜。其次，双路径机制(H2)的可能存在，可能建议管理者从"最大化互动"的单一思维，转向\textbf{"最优化体验"的平衡思维}，即在鼓励积极参与的同时，可能需要关注过度社交可能带来的心理压力。最后，调节效应(H3)的发现，可能为实施基于生命周期的\textbf{"精准用户管理"}提供了一定的参考依据：对新用户，可能应强化其社交效能感；对老用户，则可能应更注重高质量的反馈与认可。







本研究存在一定的局限性。首先，基于单一平台的数据可能限制了结果的泛化性。其次，观察性研究的性质限制了因果推断的严格性。未来研究可以在不同平台和文化背景下验证研究发现，并采用实验设计进一步验证因果机制。

\section{结论}
\label{sec:conclusion}

本研究的核心贡献可能在于，探索并在一定程度上验证了一个动态的、双路径的、且受情境调节的用户留存理论模型。研究发现，在本研究样本中，用户的留存决策可能并非由单一的线性激励驱动，而是可能在"社交激励"与"社交压力"两条并行路径的动态平衡中形成，且这种平衡机制可能会随着用户生命周期的演进而发生一定变化。这一发现可能为理解复杂的数字社会行为提供了一个新的理论视角，也可能为平台实施更为人性化和精细化的用户管理策略提供了一定的实证参考。

\end{document}
