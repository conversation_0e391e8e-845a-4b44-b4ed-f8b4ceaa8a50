#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极全面学术图表生成器 - 中英文双版本 + 更多图表
Ultimate Comprehensive Academic Charts Generator - Bilingual + More Charts
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.patches import Rectangle, FancyBboxPatch
import matplotlib.patches as mpatches
import os
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """
    设置中文字体 - 多种方法尝试
    """
    print("🔧 设置中文字体...")
    
    try:
        # 方法1: 直接设置字体族
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 'STHeiti', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 方法2: 设置字体编码
        plt.rcParams['font.family'] = 'sans-serif'
        
        print("✅ 中文字体设置完成")
        return True
        
    except Exception as e:
        print(f"❌ 中文字体设置失败: {e}")
        return False

def setup_plot_style():
    """设置图表样式"""
    plt.style.use('default')
    plt.rcParams.update({
        'figure.dpi': 100,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.facecolor': 'white',
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.labelsize': 14,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'legend.fontsize': 11,
        'figure.figsize': (12, 8)
    })

def create_chart_1_main_effects_bilingual():
    """
    图1: 四阈值主效应变化趋势图 - 中英文双版本
    """
    print("🎨 生成图1: 四阈值主效应变化趋势图（中英文双版本）...")
    
    # 数据
    variables_en = ['active_months', 'degree_centrality', 'received_comments_log',
                   'total_interactions_log', 'pagerank', 'closeness_centrality',
                   'betweenness_centrality', 'has_received_comments', 'Social_Efficacy',
                   'early_activity_log', 'Emotional_Stability']
    
    variables_cn = ['活跃月数', '度中心性', '收到评论数量', '总互动量', 'PageRank值', 
                   '接近中心性', '中介中心性', '是否收到评论', '社交效能感', 
                   '早期活动量', '情感稳定性']
    
    data = {
        '90_days': [2.5201, 1.6121, 1.5317, 1.4614, 1.1308, 1.0963, 0.8958, 0.7792, 0.5528, 0.3576, 0.1933],
        '150_days': [2.2701, 1.4221, 1.3287, 1.3040, 0.9882, 1.0071, 0.7366, 0.7096, 0.5270, 0.2795, 0.1750],
        '180_days': [2.1473, 1.3170, 1.2742, 1.2553, 0.9015, 0.9937, 0.6356, 0.7156, 0.5435, 0.2379, 0.1643],
        '330_days': [1.4256, 0.8927, 0.9612, 0.9019, 0.6530, 0.8379, 0.4819, 0.6482, 0.4523, 0.1622, 0.1572]
    }
    
    # 英文版
    fig, ax = plt.subplots(figsize=(16, 10))
    
    thresholds = ['90 days', '150 days', '180 days', '330 days']
    colors = plt.cm.Set3(np.linspace(0, 1, len(variables_en)))
    
    for i, var in enumerate(variables_en):
        values = [data['90_days'][i], data['150_days'][i], data['180_days'][i], data['330_days'][i]]
        ax.plot(thresholds, values, marker='o', linewidth=3, markersize=8, 
                label=var, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for j, (threshold, val) in enumerate(zip(thresholds, values)):
            ax.text(j, val + 0.05, f'{val:.2f}', ha='center', va='bottom', 
                   fontsize=9, fontweight='bold', color=colors[i])
    
    ax.set_title('Main Effects Trends Across Four Thresholds\n(Cohen\'s d Values)', 
                 fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('Time Thresholds', fontsize=16, fontweight='bold')
    ax.set_ylabel('Effect Size (Cohen\'s d)', fontsize=16, fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=12)
    
    # 添加效应大小参考线
    ax.axhline(y=0.2, color='gray', linestyle='--', alpha=0.5, label='Small Effect (d=0.2)')
    ax.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='Medium Effect (d=0.5)')
    ax.axhline(y=0.8, color='gray', linestyle='--', alpha=0.9, label='Large Effect (d=0.8)')
    ax.axhline(y=1.2, color='gray', linestyle='--', alpha=1.0, label='Very Large Effect (d=1.2)')
    
    plt.tight_layout()
    plt.savefig('图表/图1_四阈值主效应趋势_英文版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 中文版
    fig, ax = plt.subplots(figsize=(16, 10))
    
    thresholds_cn = ['90天', '150天', '180天', '330天']
    
    for i, var in enumerate(variables_cn):
        values = [data['90_days'][i], data['150_days'][i], data['180_days'][i], data['330_days'][i]]
        ax.plot(thresholds_cn, values, marker='o', linewidth=3, markersize=8, 
                label=var, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for j, (threshold, val) in enumerate(zip(thresholds_cn, values)):
            ax.text(j, val + 0.05, f'{val:.2f}', ha='center', va='bottom', 
                   fontsize=9, fontweight='bold', color=colors[i])
    
    ax.set_title('四阈值主效应变化趋势\n(Cohen\'s d 值)', 
                 fontsize=18, fontweight='bold', pad=20)
    ax.set_xlabel('时间阈值', fontsize=16, fontweight='bold')
    ax.set_ylabel('效应大小 (Cohen\'s d)', fontsize=16, fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=12)
    
    # 添加效应大小参考线
    ax.axhline(y=0.2, color='gray', linestyle='--', alpha=0.5)
    ax.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7)
    ax.axhline(y=0.8, color='gray', linestyle='--', alpha=0.9)
    ax.axhline(y=1.2, color='gray', linestyle='--', alpha=1.0)
    
    plt.tight_layout()
    plt.savefig('图表/图1_四阈值主效应趋势_中文版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ 图1已生成（中英文双版本）")

def create_chart_2_significance_heatmap_bilingual():
    """
    图2: 四阈值显著性热力图 - 中英文双版本
    """
    print("🎨 生成图2: 四阈值显著性热力图（中英文双版本）...")
    
    # 显著性数据
    variables_en = ['active_months', 'degree_centrality', 'received_comments_log',
                   'total_interactions_log', 'pagerank', 'closeness_centrality',
                   'betweenness_centrality', 'has_received_comments', 'Social_Efficacy',
                   'early_activity_log', 'Emotional_Stability']
    
    variables_cn = ['活跃月数', '度中心性', '收到评论数量', '总互动量', 'PageRank值', 
                   '接近中心性', '中介中心性', '是否收到评论', '社交效能感', 
                   '早期活动量', '情感稳定性']
    
    p_values = {
        '90d': [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.003, 0.052, 0.296],
        '150d': [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.007, 0.145, 0.034],
        '180d': [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.003, 0.001, 0.005, 0.217, 0.324],
        '330d': [0.001, 0.001, 0.001, 0.001, 0.003, 0.001, 0.026, 0.003, 0.034, 0.430, 0.041]
    }
    
    # 创建显著性矩阵
    significance_matrix = []
    for threshold in ['90d', '150d', '180d', '330d']:
        sig_row = [1 if p < 0.05 else 0 for p in p_values[threshold]]
        significance_matrix.append(sig_row)
    
    sig_matrix = np.array(significance_matrix)
    
    # 英文版
    fig, ax = plt.subplots(figsize=(14, 8))
    
    im = ax.imshow(sig_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    ax.set_xticks(range(len(variables_en)))
    ax.set_yticks(range(4))
    ax.set_xticklabels(variables_en, rotation=45, ha='right')
    ax.set_yticklabels(['90 days', '150 days', '180 days', '330 days'])
    
    ax.set_title('Significance Pattern Across Four Thresholds\n(Green=Significant p<0.05, Red=Non-significant p≥0.05)', 
                 fontsize=16, fontweight='bold')
    ax.set_xlabel('Variables', fontsize=14, fontweight='bold')
    ax.set_ylabel('Time Thresholds', fontsize=14, fontweight='bold')
    
    # 添加显著性标记和p值
    for i in range(4):
        for j in range(len(variables_en)):
            threshold_key = ['90d', '150d', '180d', '330d'][i]
            p_val = p_values[threshold_key][j]
            symbol = '✓' if sig_matrix[i, j] == 1 else '✗'
            ax.text(j, i, f'{symbol}\np={p_val:.3f}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=9)
    
    # 突出显示Emotional_Stability的波动模式
    emo_idx = variables_en.index('Emotional_Stability')
    ax.add_patch(Rectangle((emo_idx-0.4, -0.4), 0.8, 4.8, 
                          fill=False, edgecolor='blue', linewidth=3))
    ax.text(emo_idx, -0.8, 'Fluctuation Pattern', ha='center', va='top', 
            fontweight='bold', color='blue', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('图表/图2_四阈值显著性热力图_英文版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 中文版
    fig, ax = plt.subplots(figsize=(14, 8))
    
    im = ax.imshow(sig_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    ax.set_xticks(range(len(variables_cn)))
    ax.set_yticks(range(4))
    ax.set_xticklabels(variables_cn, rotation=45, ha='right')
    ax.set_yticklabels(['90天', '150天', '180天', '330天'])
    
    ax.set_title('四阈值显著性模式\n(绿色=显著 p<0.05, 红色=不显著 p≥0.05)', 
                 fontsize=16, fontweight='bold')
    ax.set_xlabel('变量', fontsize=14, fontweight='bold')
    ax.set_ylabel('时间阈值', fontsize=14, fontweight='bold')
    
    # 添加显著性标记和p值
    for i in range(4):
        for j in range(len(variables_cn)):
            threshold_key = ['90d', '150d', '180d', '330d'][i]
            p_val = p_values[threshold_key][j]
            symbol = '✓' if sig_matrix[i, j] == 1 else '✗'
            ax.text(j, i, f'{symbol}\np={p_val:.3f}', ha="center", va="center", 
                   color="black", fontweight='bold', fontsize=9)
    
    # 突出显示情感稳定性的波动模式
    emo_idx = variables_cn.index('情感稳定性')
    ax.add_patch(Rectangle((emo_idx-0.4, -0.4), 0.8, 4.8, 
                          fill=False, edgecolor='blue', linewidth=3))
    ax.text(emo_idx, -0.8, '波动模式', ha='center', va='top', 
            fontweight='bold', color='blue', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('图表/图2_四阈值显著性热力图_中文版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ 图2已生成（中英文双版本）")

def create_chart_3_mediation_comparison_bilingual():
    """
    图3: 正向vs负向中介效应对比图 - 中英文双版本
    """
    print("🎨 生成图3: 正向vs负向中介效应对比图（中英文双版本）...")
    
    # 中介效应数据
    variables_en = ['total_interactions', 'has_received_comments', 'received_comments',
                   'degree_centrality', 'pagerank', 'betweenness_centrality',
                   'closeness_centrality', 'active_months', 'early_activity']
    
    variables_cn = ['总互动量', '是否收到评论', '收到评论数量', '度中心性', 'PageRank值', 
                   '中介中心性', '接近中心性', '活跃月数', '早期活动量']
    
    # Social_Efficacy_score中介（正向）
    social_mediation = {
        '90d': [9.8, 18.8, 10.1, 14.7, 24.7, 14.4, 10.9, 9.3, 39.7],
        '150d': [10.4, 19.7, 11.4, 12.1, 21.3, 11.2, 11.5, 7.7, 45.3],
        '180d': [13.2, 20.9, 13.5, 13.7, 23.5, 16.5, 12.8, 9.3, 55.3],
        '330d': [13.1, 16.4, 12.2, 10.0, 16.0, 5.0, 11.1, 7.0, 56.6]
    }
    
    # Emotional_Stability_score中介（负向）
    emotional_mediation = {
        '90d': [1.3, -4.7, -3.0, 0.1, -2.6, -1.9, -1.8, 0.9, 5.3],
        '150d': [1.2, -4.8, -3.0, 0.1, -2.2, -1.5, -1.8, 0.7, 5.9],
        '180d': [1.1, -4.6, -2.9, 0.1, -2.2, -2.2, -1.7, 0.7, 6.4],
        '330d': [1.1, -5.0, -3.0, 0.1, -1.9, -0.8, -2.0, 0.7, 8.7]
    }
    
    # 英文版
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    x_pos = np.arange(len(variables_en))
    width = 0.2
    thresholds = ['90d', '150d', '180d', '330d']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    # 社交效能感中介（正向）
    for i, threshold in enumerate(thresholds):
        values = social_mediation[threshold]
        bars = ax1.bar(x_pos + i*width, values, width, 
                      label=threshold, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for bar, val in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{val:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    ax1.set_title('Social Efficacy Mediation Effects\n(Positive Pathway)', 
                  fontsize=16, fontweight='bold')
    ax1.set_xlabel('S Variables', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Mediation Percentage (%)', fontsize=14, fontweight='bold')
    ax1.set_xticks(x_pos + width * 1.5)
    ax1.set_xticklabels(variables_en, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 情感稳定性中介（负向）
    for i, threshold in enumerate(thresholds):
        values = emotional_mediation[threshold]
        bars = ax2.bar(x_pos + i*width, values, width, 
                      label=threshold, color=colors[i], alpha=0.8)
        
        # 为负值使用红色
        for bar, val in zip(bars, values):
            if val < 0:
                bar.set_color('red')
                bar.set_alpha(0.7)
            height = bar.get_height()
            y_pos = height + 0.2 if height >= 0 else height - 0.5
            ax2.text(bar.get_x() + bar.get_width()/2., y_pos,
                    f'{val:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', 
                    fontsize=9, fontweight='bold')
    
    ax2.set_title('Emotional Stability Mediation Effects\n(Negative Pathway Discovery)', 
                  fontsize=16, fontweight='bold')
    ax2.set_xlabel('S Variables', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Mediation Percentage (%)', fontsize=14, fontweight='bold')
    ax2.set_xticks(x_pos + width * 1.5)
    ax2.set_xticklabels(variables_en, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    # 添加注释
    ax2.text(0.02, 0.98, 'Red bars indicate negative mediation effects\n(Social pressure mechanism)', 
            transform=ax2.transAxes, fontsize=12, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('图表/图3_正向vs负向中介效应对比_英文版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 中文版
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # 社交效能感中介（正向）
    for i, threshold in enumerate(['90天', '150天', '180天', '330天']):
        threshold_key = ['90d', '150d', '180d', '330d'][i]
        values = social_mediation[threshold_key]
        bars = ax1.bar(x_pos + i*width, values, width, 
                      label=threshold, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for bar, val in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{val:.1f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    ax1.set_title('社交效能感中介效应\n(正向路径)', 
                  fontsize=16, fontweight='bold')
    ax1.set_xlabel('S变量', fontsize=14, fontweight='bold')
    ax1.set_ylabel('中介比例 (%)', fontsize=14, fontweight='bold')
    ax1.set_xticks(x_pos + width * 1.5)
    ax1.set_xticklabels(variables_cn, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 情感稳定性中介（负向）
    for i, threshold in enumerate(['90天', '150天', '180天', '330天']):
        threshold_key = ['90d', '150d', '180d', '330d'][i]
        values = emotional_mediation[threshold_key]
        bars = ax2.bar(x_pos + i*width, values, width, 
                      label=threshold, color=colors[i], alpha=0.8)
        
        # 为负值使用红色
        for bar, val in zip(bars, values):
            if val < 0:
                bar.set_color('red')
                bar.set_alpha(0.7)
            height = bar.get_height()
            y_pos = height + 0.2 if height >= 0 else height - 0.5
            ax2.text(bar.get_x() + bar.get_width()/2., y_pos,
                    f'{val:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', 
                    fontsize=9, fontweight='bold')
    
    ax2.set_title('情感稳定性中介效应\n(负向路径发现)', 
                  fontsize=16, fontweight='bold')
    ax2.set_xlabel('S变量', fontsize=14, fontweight='bold')
    ax2.set_ylabel('中介比例 (%)', fontsize=14, fontweight='bold')
    ax2.set_xticks(x_pos + width * 1.5)
    ax2.set_xticklabels(variables_cn, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8)
    
    # 添加注释
    ax2.text(0.02, 0.98, '红色柱状图表示负向中介效应\n(社交压力机制)', 
            transform=ax2.transAxes, fontsize=12, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('图表/图3_正向vs负向中介效应对比_中文版.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print("✅ 图3已生成（中英文双版本）")

if __name__ == "__main__":
    # 设置字体和样式
    setup_chinese_font()
    setup_plot_style()
    
    # 确保文件夹存在
    os.makedirs('图表', exist_ok=True)
    
    # 生成前3个图表
    create_chart_1_main_effects_bilingual()
    create_chart_2_significance_heatmap_bilingual()
    create_chart_3_mediation_comparison_bilingual()
    
    print(f"\n🎉 前3个图表生成完成！")
    print(f"📁 图表保存在: 图表/ 文件夹")
    print(f"📊 已生成中英文双版本图表：")
    print(f"   ✅ 图1_四阈值主效应趋势_英文版/中文版")
    print(f"   ✅ 图2_四阈值显著性热力图_英文版/中文版")
    print(f"   ✅ 图3_正向vs负向中介效应对比_英文版/中文版")
    print(f"\n🔄 继续生成更多图表...")
